package quoteprivate

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/pricing"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	customerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	"github.com/drumkitai/drumkit/common/redis"
)

type (
	SearchLTLCustomersQuery struct {
		Name string `query:"name"`
	}

	GetLTLCustomersResponse struct {
		CustomerList []models.TMSCustomer `json:"customerList"`
		TMSTenant    string               `json:"tmsTenant"`
	}
)

// Searches for customers in the integration by name.
func SearchLTLCustomers(c *fiber.Ctx) error {
	ctx := log.With(c.UserContext())
	var query SearchLTLCustomersQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		log.Error(ctx, "invalid request query", zap.Error(err))
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "failed to get user by id", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if userServiceID != user.ServiceID {
		log.Infof(
			ctx,
			"claims serviceID %d does not match user service ID %d",
			userServiceID,
			user.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	service, err := rds.GetServiceByID(ctx, user.ServiceID)
	if err != nil {
		log.Error(ctx, "failed to get service by id", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	pricingIntegrations, err := integrationDB.GetPricingListByServiceID(ctx, service.ID)
	if err != nil {
		log.Error(ctx, "error fetching pricing integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var taiIntegration models.Integration
	for _, integration := range pricingIntegrations {
		if integration.Name == models.TaiPricing {
			taiIntegration = integration
			break
		}
	}

	if taiIntegration.ID == 0 {
		log.Error(ctx, "no TAI pricing integration found for service")
		return c.SendStatus(http.StatusNotFound)
	}

	client, err := pricing.New(ctx, taiIntegration, models.WithUserEmail(user.EmailAddress))
	if err != nil {
		log.Error(ctx, "failed to create pricing client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	cacheKey := fmt.Sprintf("ltl-customers-search:%d:%s", service.ID, query.Name)

	var customerList []models.TMSCustomer
	var fromCache bool

	if query.Name != "" {
		cachedCustomers, found, err := redis.GetKey[[]models.TMSCustomer](ctx, cacheKey)
		if err != nil {
			log.Warn(
				ctx,
				"Error retrieving LTL customers from cache",
				zap.String("cacheKey", cacheKey),
				zap.Error(err),
			)
		} else if found {
			log.Infof(ctx, "Cache hit for LTL customers search", zap.String("cacheKey", cacheKey))
			customerList = cachedCustomers
			fromCache = true
		}
	} else {
		log.Debug(ctx, "Empty search query, skipping cache lookup.")
	}

	// If not found in cache, search via API
	if !fromCache {
		var err error
		customerList, err = client.SearchCustomers(ctx, query.Name)
		if err != nil {
			log.Error(ctx, "error searching TMS customers in integration", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	// Save customers to RDS only if we fetched from API (not from cache)
	// Always use TAI pricing integration ID since these customers come from TAI pricing API.
	// This prevents overwriting TAI TMS customers (which have full address data) when a service
	// has both TAI TMS and TAI pricing integrations. The unique constraint is (tms_integration_id, external_tms_id),
	// so using different integration IDs keeps TAI TMS and TAI pricing customers separate.
	if !fromCache && len(customerList) > 0 {
		integrationID := taiIntegration.ID

		// Set TMSIntegrationID on all customers before saving
		for i := range customerList {
			customerList[i].TMSIntegrationID = integrationID
		}

		// Save to RDS - RefreshTMSCustomers handles uniqueness constraint
		// (tms_integration_id + external_tms_id) to prevent duplicates
		err = customerDB.RefreshTMSCustomers(ctx, &customerList)
		if err != nil {
			log.Warn(
				ctx,
				"Failed to save TAI customers to RDS",
				zap.Error(err),
				zap.Int("customerCount", len(customerList)),
			)
			// Don't fail the request if RDS save fails - still return the search results
		} else {
			log.Info(
				ctx,
				"Successfully saved TAI customers to RDS",
				zap.Int("customerCount", len(customerList)),
				zap.Uint("integrationID", integrationID),
			)
		}
	}

	// Only cache if we fetched from API (not from cache)
	if query.Name != "" && len(customerList) > 0 && !fromCache {
		cacheExpiration := 1 * time.Hour
		err := redis.SetKey(
			ctx,
			cacheKey,
			customerList,
			cacheExpiration,
		)
		if err != nil {
			log.Warn(
				ctx,
				"Failed to cache LTL customers search results",
				zap.String("cacheKey", cacheKey),
				zap.Error(err),
			)
		} else {
			log.Infof(
				ctx,
				"Cached LTL customers search results",
				zap.String("cacheKey", cacheKey),
			)
		}
	}

	return c.Status(http.StatusOK).JSON(
		GetLTLCustomersResponse{
			CustomerList: customerList,
			TMSTenant:    taiIntegration.Tenant,
		},
	)
}
