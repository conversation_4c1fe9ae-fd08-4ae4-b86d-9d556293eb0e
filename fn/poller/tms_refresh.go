package main

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/sentry"
)

var (
	// For unit testing
	dbFuncGetAllValidTMS               = integrationDB.GetAllValidTMS
	dbFuncSetColumn                    = integrationDB.SetColumn
	redisFuncGetIntegrationStateStatus = redis.GetIntegrationStateWithStatus
	redisFuncGetTMSRefreshLastRun      = redis.GetTMSRefreshLastRun
	redisFuncSetTMSRefreshLastRun      = redis.SetTMSRefreshLastRun
	redisFuncSetIntegrationStateStatus = redis.SetIntegrationStateWithStatus
	redisFuncClearIntegrationState     = redis.ClearIntegrationState
	tmsFuncNew                         = tms.New
)

type tmsJobConfig struct {
	jobType      string
	jobName      string
	runFn        func(ctx context.Context, client tms.Interface, opts ...models.TMSOption) error
	updateColumn integrationDB.Column
}

// TMS types that support partial syncs via updatedAt filter
var partialSyncTMSes = map[models.IntegrationName]bool{
	models.McleodEnterprise: true,
	models.Turvo:            true,
	models.ThreePLSystems:   true,
	models.Revenova:         true,
}

// TMS types that require full syncs
var fullSyncTMSes = map[models.IntegrationName]bool{
	models.Aljex:  true,
	models.ThreeG: true,
	models.Relay:  true,
	models.Ascend: true,
}

const (
	PollerMaxDuration = 10 * time.Minute
	TMSJobMaxDuration = PollerMaxDuration - 1*time.Minute
)

// shouldRunTMSRefresh checks if TMS refresh should run based on cadence
func shouldRunTMSRefresh(ctx context.Context, integration models.Integration, jobType string) bool {
	// check if we have any in progress job for the integration
	status, _, _, err := redisFuncGetIntegrationStateStatus(
		ctx,
		integration.ID,
		jobType,
	)
	if err != nil {
		log.WarnNoSentry(ctx, "failed to read job state", zap.Error(err))
	}

	// Always resume in-progress jobs
	if status == redis.JobStatusInProgress {
		log.Debug(
			ctx,
			"resuming in-progress TMS job",
			zap.Uint("integrationID", integration.ID),
			zap.String("jobType", jobType),
		)
		return true
	}

	lastRun, err := redisFuncGetTMSRefreshLastRun(ctx, integration.ID, jobType)
	if err != nil {
		log.WarnNoSentry(ctx, "could not get last TMS refresh run time, will run", zap.Error(err))
		return true
	}

	if lastRun.IsZero() {
		log.Info(ctx, "no previous TMS refresh run recorded, will run")
		return true
	}

	var interval time.Duration
	switch {
	case partialSyncTMSes[integration.Name]:
		// Partial sync TMSes run every hour
		interval = time.Hour

	case fullSyncTMSes[integration.Name]:
		// Full sync TMSes run once a day during off hours (7-8 PM EST)
		interval = 24 * time.Hour

		// For full sync TMSes, also check if we're in the right time window (7-8 PM EST)
		now := time.Now()
		est, err := time.LoadLocation("America/New_York")
		if err != nil {
			log.Error(ctx, "error loading EST timezone, will not run TMS refresh", zap.Error(err))
			return false
		}

		nowEST := now.In(est)
		hour := nowEST.Hour()

		// Only run between 7-8 PM EST
		if hour < 19 || hour >= 20 {
			log.Debug(
				ctx,
				"not in TMS full sync time window (7-8 PM EST)",
				zap.Int("currentHour", hour),
			)
			return false
		}

	default:
		log.Warn(
			ctx,
			"TMS type not recognized, will not run TMS refresh",
			zap.String("tmsName", string(integration.Name)),
		)
		return false
	}

	timeSinceLastRun := time.Since(lastRun)
	shouldRun := timeSinceLastRun >= interval

	log.Info(
		ctx,
		"TMS refresh timing check",
		zap.String("tmsName", string(integration.Name)),
		zap.Duration("timeSinceLastRun", timeSinceLastRun),
		zap.Duration("interval", interval),
		zap.Bool("shouldRun", shouldRun),
	)

	return shouldRun
}

// handleTMSRefresh handles TMS customer and location refresh for all integrations
func handleTMSRefresh(ctx context.Context) {
	sentry.WithHub(ctx, func(ctx context.Context) {
		log.Info(ctx, "starting TMS refresh check")

		// Get all TMS integrations
		integrations, err := dbFuncGetAllValidTMS(ctx)
		if err != nil {
			log.Error(ctx, "failed to get TMS integrations", zap.Error(err))
			return
		}

		var wg sync.WaitGroup

		for _, integration := range integrations {
			// Check if we should refresh customers
			if shouldRunTMSRefresh(ctx, integration, redis.CustomerRefreshJob) {
				wg.Add(1)
				go func(integ models.Integration) {
					defer wg.Done()
					customerJob := tmsJobConfig{
						jobType:      redis.CustomerRefreshJob,
						jobName:      "customers",
						updateColumn: integrationDB.LastCustomerUpdatedAt,
						runFn: func(ctx context.Context, client tms.Interface, opts ...models.TMSOption) error {
							_, err := client.GetCustomers(ctx, opts...)
							return err
						},
					}
					if err := refreshTMSJob(ctx, integ, customerJob); err != nil {
						log.Error(
							ctx,
							"TMS customer refresh failed",
							zap.Error(err),
							zap.Uint("integrationID", integ.ID),
							zap.String("tmsName", string(integ.Name)),
						)
					}
				}(integration)
			}

			// Check if we should refresh locations
			if shouldRunTMSRefresh(ctx, integration, redis.LocationRefreshJob) {
				wg.Add(1)
				go func(integ models.Integration) {
					defer wg.Done()
					locationJob := tmsJobConfig{
						jobType:      redis.LocationRefreshJob,
						jobName:      "locations",
						updateColumn: integrationDB.LastLocationUpdatedAt,
						runFn: func(ctx context.Context, client tms.Interface, opts ...models.TMSOption) error {
							_, err := client.GetLocations(ctx, opts...)
							return err
						},
					}
					if err := refreshTMSJob(ctx, integ, locationJob); err != nil {
						log.Error(
							ctx,
							"TMS location refresh failed",
							zap.Error(err),
							zap.Uint("integrationID", integ.ID),
							zap.String("tmsName", string(integ.Name)),
						)
					}
				}(integration)
			}
		}

		wg.Wait()
		log.Info(ctx, "TMS refresh check completed")
	})
}

func refreshTMSJob(
	ctx context.Context,
	integration models.Integration,
	jobConfig tmsJobConfig,
) error {
	ctx, cancel := context.WithTimeout(ctx, TMSJobMaxDuration) // Leave 1 minute buffer before poller timeout
	defer cancel()

	jobType := jobConfig.jobType
	runFn := jobConfig.runFn
	updateColumn := jobConfig.updateColumn
	jobName := jobConfig.jobName

	log.Info(
		ctx,
		fmt.Sprintf("starting TMS %s refresh", jobName),
		zap.Uint("integrationID", integration.ID),
		zap.String("tmsName", string(integration.Name)),
	)

	// Check for existing job state to continue (using status-aware function)
	savedStatus, savedUpdatedAt, savedCursor, err := redisFuncGetIntegrationStateStatus(ctx, integration.ID, jobType)
	if err != nil {
		log.WarnNoSentry(
			ctx,
			fmt.Sprintf("failed to get integration state for %s refresh", jobName),
			zap.Error(err))
	}

	log.Info(
		ctx,
		fmt.Sprintf("TMS %s refresh job state", jobName),
		zap.String("savedStatus", savedStatus),
		zap.String("savedUpdatedAt", savedUpdatedAt),
		zap.String("savedCursor", savedCursor),
		zap.Uint("integrationID", integration.ID),
	)

	// Check if another job is already in progress
	if savedStatus == redis.JobStatusInProgress && savedCursor == "" && savedUpdatedAt == "" {
		// This could be either:
		// 1. A fresh in_progress marker (another poller just started)
		// 2. A timed-out job that never made progress (stuck job)
		//
		// To distinguish between these cases, check when the job was last updated.
		// For TMSes that don't implement OnProgress (like Aljex, ThreePLSystems),
		// we need to check if enough time has passed to consider the job stuck.

		lastRun, err := redisFuncGetTMSRefreshLastRun(ctx, integration.ID, jobType)
		if err != nil {
			log.WarnNoSentry(ctx, "could not get last TMS refresh run time for stuck job check", zap.Error(err))
			// If we can't determine when the job started, err on the side of caution and skip
			log.Info(
				ctx,
				fmt.Sprintf("%s refresh job already in progress, skipping (could not verify if stuck)", jobName),
				zap.Uint("integrationID", integration.ID),
			)
			return nil
		}

		// If the job was started recently (within the last 30 minutes), assume another poller is running
		// 30 minutes should be enough for most TMS operations, even full syncs
		const stuckJobThreshold = 30 * time.Minute
		timeSinceStart := time.Since(lastRun)

		if timeSinceStart < stuckJobThreshold {
			log.Info(
				ctx,
				fmt.Sprintf("%s refresh job already in progress, skipping (started %v ago)", jobName, timeSinceStart.Round(time.Second)),
				zap.Uint("integrationID", integration.ID),
				zap.Duration("timeSinceStart", timeSinceStart),
			)
			return nil
		}

		// Job has been stuck for too long - clear the state and proceed
		log.Warn(
			ctx,
			fmt.Sprintf("%s refresh job appears stuck (no progress for %v), clearing state and proceeding", jobName, timeSinceStart.Round(time.Second)),
			zap.Uint("integrationID", integration.ID),
			zap.Duration("timeSinceStart", timeSinceStart),
		)

		// Clear the stuck state
		if err := redisFuncClearIntegrationState(ctx, integration.ID, jobType); err != nil {
			log.WarnNoSentry(ctx, "failed to clear stuck job state", zap.Error(err))
		}

		// Reset saved values so the job can proceed normally
		savedStatus = ""
		savedCursor = ""
		savedUpdatedAt = ""
	}

	client, err := tmsFuncNew(ctx, integration)
	if err != nil {
		return fmt.Errorf("error creating TMS client: %w", err)
	}

	// Set lastRun at job start for full sync TMSes
	if fullSyncTMSes[integration.Name] && savedStatus != redis.JobStatusInProgress {
		if err := redisFuncSetTMSRefreshLastRun(ctx, integration.ID, jobType); err != nil {
			log.WarnNoSentry(ctx, "failed to set lastRun at job start", zap.Error(err))
		}
	}

	var (
		lastUpdatedAt string
		lastCursor    string
	)
	// Build options for TMS client
	opts := []models.TMSOption{
		models.WithPollerJob(true),
		models.WithProgressCallback(func(u, c string) {
			lastUpdatedAt = u
			lastCursor = c
		}),
	}

	// If we have a saved job state, use it (job continuation)
	if savedUpdatedAt != "" || savedCursor != "" {
		log.Info(
			ctx,
			fmt.Sprintf("continuing previous %s refresh job", jobName),
			zap.String("savedStatus", savedStatus),
			zap.String("savedUpdatedAt", savedUpdatedAt),
			zap.String("savedCursor", savedCursor),
			zap.Uint("integrationID", integration.ID),
		)

		if savedUpdatedAt != "" {
			opts = append(opts, models.WithUpdatedAtFilter(savedUpdatedAt))
		}
		if savedCursor != "" {
			opts = append(opts, models.WithCursor(savedCursor))
		}
	} else if partialSyncTMSes[integration.Name] {
		// New job - use timestamp filtering for partial sync TMSes
		opts = append(opts, models.WithUseLastUpdatedAt(true))
	}

	// Mark job as in progress (at start or continuation)
	// TMS implementation will update this during execution
	if savedCursor == "" {
		// Only set in_progress at the very start (no cursor yet)
		if setErr := redisFuncSetIntegrationStateStatus(
			ctx,
			integration.ID,
			jobType,
			redis.JobStatusInProgress,
			"", // updatedAt will be set by TMS implementation
			"", // cursor will be set by TMS implementation
		); setErr != nil {
			log.WarnNoSentry(ctx, "failed to set initial in_progress state", zap.Error(setErr))
		}
	}

	// Execute the refresh - the TMS implementation will handle pagination and timeout
	if err := runFn(ctx, client, opts...); err != nil {
		// Before Lambda max timeout, store TMS integration state
		if errors.Is(err, context.DeadlineExceeded) {
			saveCtx, cancel := context.WithTimeout(
				context.WithoutCancel(ctx),
				10*time.Second,
			)
			defer cancel()
			if err := redisFuncSetIntegrationStateStatus(
				saveCtx,
				integration.ID,
				jobType,
				redis.JobStatusInProgress,
				lastUpdatedAt,
				lastCursor,
			); err != nil {
				log.WarnNoSentry(ctx, "failed to set integration state", zap.Error(err))
			}
			return nil
		}
		// TMS implementation already saved state on error/timeout
		return fmt.Errorf("failed to refresh %s: %w", jobName, err)
	}

	// Mark successful completion
	// Only partial sync TMSes should update lastRun on completion
	if partialSyncTMSes[integration.Name] {
		if err := redisFuncSetTMSRefreshLastRun(ctx, integration.ID, jobType); err != nil {
			log.WarnNoSentry(
				ctx,
				fmt.Sprintf("failed to update %s refresh last run time", jobName),
				zap.Error(err),
			)
		}
	}

	// Clear the job state from Redis (successful completion)
	if err := redisFuncClearIntegrationState(ctx, integration.ID, jobType); err != nil {
		log.WarnNoSentry(
			ctx,
			fmt.Sprintf("failed to clear %s refresh state", jobName),
			zap.Error(err))
	}

	// Update integration timestamp only on full completion
	if err := dbFuncSetColumn(
		ctx,
		integration.ID,
		updateColumn,
		time.Now(),
	); err != nil {
		log.Warn(
			ctx,
			fmt.Sprintf("failed to update integration %s timestamp", jobName),
			zap.Error(err))
	}

	log.Info(
		ctx,
		fmt.Sprintf("TMS %s refresh completed successfully", jobName),
		zap.Uint("integrationID", integration.ID),
		zap.String("tmsName", string(integration.Name)),
	)

	return nil
}
