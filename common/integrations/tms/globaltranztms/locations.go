package globaltranztms

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
)

const (
	GetAddressBookPath = "api/tms-addressbook/AddressBook/GetAddressesByCustomerBK"
)

type AddressBookResponse struct {
	Message          *string           `json:"message"`
	DidError         bool              `json:"didError"`
	ErrorMessages    []string          `json:"errorMessages"`
	ObjectWasCreated bool              `json:"objectWasCreated"`
	Model            []AddressBookItem `json:"model"`
}

type AddressBookItem struct {
	AddressBK      int64              `json:"addressBK"`
	CompanyName    string             `json:"companyName"`
	Contact        AddressBookContact `json:"contact"`
	Address        AddressBookAddress `json:"address"`
	DockHours      *string            `json:"dockHours"`
	LocationNotes  *string            `json:"locationNotes"`
	DefaultGroupID int                `json:"defaultGroupId"`
	Remarks        *string            `json:"remarks"`
}

type AddressBookContact struct {
	Name  string  `json:"name"`
	Phone string  `json:"phone"`
	Email *string `json:"email"`
	Fax   *string `json:"fax"`
}

type AddressBookAddress struct {
	Street1   string  `json:"street1"`
	Street2   string  `json:"street2"`
	City      string  `json:"city"`
	State     string  `json:"state"`
	Zip       string  `json:"zip"`
	Country   int     `json:"country"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

func (gt GlobalTranz) GetLocations(ctx context.Context, _ ...models.TMSOption) ([]models.TMSLocation, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetLocationsGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	customers, err := tmsCustomerDB.GetTMSCustomersByTMSID(ctx, rds.GenericGetQuery{
		TMSID: gt.tms.ID,
	})
	if err != nil {
		log.Error(
			ctx,
			"failed to get TMS customers",
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get TMS customers: %w", err)
	}

	log.Info(
		ctx,
		"fetching locations for GlobalTranz customers",
		zap.Int("customerCount", len(customers)),
	)

	var allLocations []models.TMSLocation
	successfulBatches := 0
	failedBatches := 0

	for _, customer := range customers {
		if customer.ExternalTMSID == "" {
			log.WarnNoSentry(
				ctx,
				"customer has no external TMS ID, skipping",
				zap.Uint("customerId", customer.ID),
				zap.String("customerName", customer.Name),
			)
			continue
		}

		locations, fetchErr := gt.getLocationsByCustomer(ctx, customer.ExternalTMSID)
		if fetchErr != nil {
			log.Error(
				ctx,
				"failed to fetch locations for customer",
				zap.String("customerBK", customer.ExternalTMSID),
				zap.String("customerName", customer.Name),
				zap.Error(fetchErr),
			)
			failedBatches++
			continue
		}

		log.Info(
			ctx,
			"fetched locations for customer",
			zap.String("customerName", customer.Name),
			zap.Int("locationCount", len(locations)),
		)

		if len(locations) > 0 {
			if saveErr := tmsLocationDB.RefreshTMSLocations(ctx, &locations); saveErr != nil {
				log.Error(
					ctx,
					"failed to save locations for customer",
					zap.String("customerBK", customer.ExternalTMSID),
					zap.String("customerName", customer.Name),
					zap.Error(saveErr),
				)
				failedBatches++
				continue
			}

			log.Info(
				ctx,
				"saved locations for customer",
				zap.String("customerName", customer.Name),
				zap.Int("locationCount", len(locations)),
			)
		}

		allLocations = append(allLocations, locations...)
		successfulBatches++
	}

	// If 0 batches succeeded, mark the span as failed
	if successfulBatches == 0 && failedBatches > 0 {
		err = fmt.Errorf("all %d customer batches failed to fetch or save locations", failedBatches)
		log.Error(
			ctx,
			"all customer batches failed",
			zap.Int("failedBatches", failedBatches),
			zap.Error(err),
		)
	}

	return allLocations, nil
}

func (gt *GlobalTranz) getLocationsByCustomer(ctx context.Context, customerBK string) ([]models.TMSLocation, error) {
	queryParams := url.Values{}
	queryParams.Set("customerBK", customerBK)

	var response AddressBookResponse
	err := gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		GetAddressBookPath,
		queryParams,
		nil,
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"API request failed",
			zap.String("customerBK", customerBK),
			zap.Error(err),
		)
		return nil, fmt.Errorf("API request failed: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if len(response.ErrorMessages) > 0 {
			errorMsg = response.ErrorMessages[0]
		}
		log.Error(
			ctx,
			"API returned error",
			zap.String("customerBK", customerBK),
			zap.String("errorMessage", errorMsg),
		)
		return nil, fmt.Errorf("API returned error: %s", errorMsg)
	}

	locations := make([]models.TMSLocation, 0, len(response.Model))
	for _, item := range response.Model {
		location := toTMSLocation(gt.tms.ID, item)
		locations = append(locations, location)
	}

	return locations, nil
}

func toTMSLocation(tmsIntegrationID uint, item AddressBookItem) models.TMSLocation {
	externalTMSID := strconv.FormatInt(item.AddressBK, 10)

	// Convert country code to ISO 2-letter country code
	var country string
	switch item.Address.Country {
	case 1:
		country = "US" // USA
	case 2:
		country = "CA" // Canada
	case 3:
		country = "CN" // China
	case 4:
		country = "MX" // Mexico
	default:
		country = "" // Unknown country code
	}

	email := ""
	if item.Contact.Email != nil && *item.Contact.Email != "" {
		email = *item.Contact.Email
	}

	notes := ""
	if item.LocationNotes != nil && *item.LocationNotes != "" {
		notes = *item.LocationNotes
	}
	if item.Remarks != nil && *item.Remarks != "" {
		if notes != "" {
			notes += " | "
		}
		notes += *item.Remarks
	}

	companyCoreInfo := models.CompanyCoreInfo{
		ExternalTMSID: externalTMSID,
		Name:          item.CompanyName,
		AddressLine1:  item.Address.Street1,
		AddressLine2:  item.Address.Street2,
		City:          item.Address.City,
		State:         item.Address.State,
		Zipcode:       item.Address.Zip,
		Country:       country,
		Contact:       item.Contact.Name,
		Phone:         item.Contact.Phone,
		Email:         email,
	}

	location := models.TMSLocation{
		TMSIntegrationID: tmsIntegrationID,
		CompanyCoreInfo:  companyCoreInfo,
		Latitude:         item.Address.Latitude,
		Longitude:        item.Address.Longitude,
		Point: models.Point{
			Latitude:  float32(item.Address.Latitude),
			Longitude: float32(item.Address.Longitude),
		},
		Notes: notes,
	}

	location.FullAddress = models.ConcatAddress(companyCoreInfo)
	location.NameAddress = location.Name + ", " + location.FullAddress

	return location
}
