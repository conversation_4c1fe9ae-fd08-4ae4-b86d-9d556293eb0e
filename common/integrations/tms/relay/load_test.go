package relay

import (
	"context"
	"encoding/json"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/models"
)

func TestParseLoadViaWebSocket(t *testing.T) {
	ctx := context.Background()
	freightTrackingID := "8080656"
	mock := mockClient{}
	tms := models.Integration{Model: gorm.Model{ID: 3}, Name: models.Relay, Type: models.TMS, ServiceID: 1}
	relay := New(ctx, tms)

	loadRespInByte, err := mock.getLoadViaWebSocket(ctx, freightTrackingID)
	require.NoError(t, err)

	var loadResp LoadResp
	err = json.Unmarshal(loadRespInByte, &loadResp)
	require.NoError(t, err)

	load, err := relay.toLoadModel(ctx, loadResp.Data.Loads[0])
	require.NoError(t, err)

	pickupTimezone, err := timezone.GetTimezone(ctx, "Shepherdsville", "KY", "")
	require.NoError(t, err)
	pickupLocation, err := time.LoadLocation(pickupTimezone)
	require.NoError(t, err)
	appointTime, err := time.ParseInLocation("2006-01-02T15:04:05", "2024-12-11T12:00:00", pickupLocation)
	require.NoError(t, err)
	readyDate, err := time.ParseInLocation("2006-01-02", "2024-12-10", pickupLocation)
	require.NoError(t, err)

	deliveryTimezone, err := timezone.GetTimezone(ctx, "Chicago", "IL", "")
	require.NoError(t, err)

	deliveryLocation, err := time.LoadLocation(deliveryTimezone)
	require.NoError(t, err)

	deliveryTime, err := time.ParseInLocation("2006-01-02T15:04:05", "2024-12-29T10:00:00", deliveryLocation)
	require.NoError(t, err)

	expectedLoad := models.Load{
		ServiceID:         tms.ServiceID,
		TMSID:             tms.ID,
		ExternalTMSID:     "3053476",
		FreightTrackingID: "8080656",
		LoadCoreInfo: models.LoadCoreInfo{
			Operator: "Jinyan Zang",
			PONums:   "*********",
			Mode:     "TL",
			Specifications: models.Specifications{
				TotalDistance:       models.ValueUnit{Val: 649.3, Unit: models.MilesUnit},
				TotalWeight:         models.ValueUnit{Val: 4000, Unit: models.LbsUnit},
				TotalOutPalletCount: 20,
			},
			Customer: models.Customer{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name: "allbirds",
				},
				RefNumber: "4512545-TestLoad",
			},
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:          "AxleTestShipper, Shepherdsville, KY",
					City:          "Shepherdsville",
					State:         "KY",
					Zipcode:       "40165",
					ExternalTMSID: "598459ae-bb97-4525",
				},
				ExternalTMSStopID: "a3544068-78f4-45c1",
				ReadyTime: models.NullTime{
					Time:  readyDate,
					Valid: true,
				},
				ApptType: "appointment",
				ApptStartTime: models.NullTime{
					Time:  appointTime,
					Valid: true,
				},
				Timezone: "America/New_York",
			},
			Consignee: models.Consignee{
				CompanyCoreInfo: models.CompanyCoreInfo{
					ExternalTMSID: "41006cc1-6d77-4376-b7a8",
					Name:          "TestReceiver",
					City:          "Chicago",
					State:         "IL",
					Zipcode:       "31407",
				},
				ExternalTMSStopID: "32d56705-74e1-49a6-bb2f",
				ApptType:          "appointment",
				ApptStartTime:     models.NullTime{Time: deliveryTime, Valid: true},
				RefNumber:         "DEL_REF123",
				Timezone:          "America/Chicago",
			},
		},
	}

	assert.Equal(t, expectedLoad, load)
}

func TestParseCarrierInfoHTML(t *testing.T) {
	ctx := context.Background()
	tms := models.Integration{Model: gorm.Model{ID: 5}, ServiceID: 3,
		APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	data, err := os.ReadFile("testdata/trackingboard.html")
	require.NoError(t, err)

	var loadData models.Load
	err = r.mapCarrierInfoHTML(ctx, data, &loadData)
	require.NoError(t, err)

	loc, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	vFalse := false

	expectedLoad := models.Load{
		TMSID:             tms.ID,
		ServiceID:         tms.ServiceID,
		ExternalTMSID:     "3054047",
		FreightTrackingID: "8081291",
		LoadCoreInfo: models.LoadCoreInfo{
			Status: "Delivered",
			Customer: models.Customer{
				RefNumber: "987678124",
			},
			Carrier: models.Carrier{
				Name:                 "SUPERFAST TRANSPORT LLC",
				FirstDriverName:      "John Doe",
				FirstDriverPhone:     "(*************",
				SecondDriverName:     "Roman",
				SecondDriverPhone:    "(*************.",
				Dispatcher:           "Jane Doe",
				Notes:                "Contact dispatcher via email only",
				Phone:                "************",
				Email:                "<EMAIL>",
				ExternalTMSTruckID:   "505",
				ExternalTMSTrailerID: "0001",
				DispatchCity:         "",
				DispatchState:        "",
			},
			Notes: models.Notes{{

				Note:        "Updated DEL ETA 7/16 1800",
				UpdatedBy:   "Jane Doe",
				Source:      "dispatcher",
				IsException: &vFalse,
				CreatedAt: models.NullTime{Time: time.Date(2024, 7, 16, 11, 22, 0, 0, loc),
					Valid: true},
			},
				{
					Note:        "PU ETA 7/13 driver held up at prior load",
					UpdatedBy:   "Jane Doe",
					Source:      "dispatcher",
					IsException: &vFalse,
					CreatedAt: models.NullTime{Time: time.Date(2024, 7, 12, 9, 33, 0, 0, loc),
						Valid: true},
				}},
		},
	}

	assert.Equal(t, expectedLoad, loadData)
}

func TestMapLoadBoardHTML(t *testing.T) {
	ctx := context.Background()
	tms := models.Integration{Model: gorm.Model{ID: 5}, ServiceID: 3,
		APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	data, err := os.ReadFile("testdata/load_board_detail.html")
	require.NoError(t, err)

	loadData, err := r.mapLoadDetailHTML(ctx, data)
	require.NoError(t, err)

	layout := "2006-01-02 15:04"
	nyLoc, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	chiLoc, err := time.LoadLocation("America/Chicago")
	require.NoError(t, err)

	pickupAppt, err := time.ParseInLocation(layout, "2024-07-12 10:00", nyLoc)
	require.NoError(t, err)

	pickupApptEnd, err := time.ParseInLocation(layout, "2024-07-12 22:00", nyLoc)
	require.NoError(t, err)

	delApptStart, err := time.ParseInLocation(layout, "2024-07-13 00:01", chiLoc)
	require.NoError(t, err)

	delApptEnd, err := time.ParseInLocation(layout, "2024-07-13 23:00", chiLoc)
	require.NoError(t, err)

	expectedLoad := models.Load{
		TMSID:             tms.ID,
		ServiceID:         tms.ServiceID,
		DeclaredValueUSD:  1200,
		ExternalTMSID:     "3011313",
		FreightTrackingID: "8081291",

		LoadCoreInfo: models.LoadCoreInfo{
			PONums:           "*********,92162772",
			Operator:         "Open Board",
			Mode:             "TL",
			MoreThanTwoStops: false,
			RateData: models.RateData{
				// CarrierLHRateUSD:  0.45006898,
				// CustomerLHRateUSD: 1.5295019,
				CarrierMaxRate: models.Ptr(float32(1200)),
			},
			Specifications: models.Specifications{
				Commodities:   "Cupcakes",
				TotalPieces:   models.ValueUnit{Val: 1, Unit: "boxes"},
				TotalDistance: models.ValueUnit{Val: 652.5, Unit: models.MilesUnit},
				TotalWeight:   models.ValueUnit{Val: 230, Unit: models.LbsUnit},
			},
			Customer: models.Customer{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name: "TEST CUSTOMER",
				},
				RefNumber: "*********",
			},
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         "Test Shipper",
					AddressLine1: "389 Fitzgerald Rd",
					City:         "Durham",
					State:        "NC",
					Zipcode:      "27299",
				},
				ApptStartTime: models.NullTime{
					Time:  pickupAppt,
					Valid: true,
				},
				ApptEndTime: models.NullTime{
					Time:  pickupApptEnd,
					Valid: true,
				},
				RefNumber: "*********",
				Timezone:  "America/New_York",
			},
			Consignee: models.Consignee{
				CompanyCoreInfo: models.CompanyCoreInfo{
					ExternalTMSID: "",
					Name:          "Test Receiver",
					AddressLine1:  "228 Newbury Dr",
					AddressLine2:  "",
					City:          "Tupelo",
					State:         "MS",
					Zipcode:       "38671",
				},
				BusinessHours: "",
				RefNumber:     "92162772",
				MustDeliver:   models.NullTime{},
				ApptStartTime: models.NullTime{
					Time:  delApptStart,
					Valid: true,
				},
				ApptEndTime: models.NullTime{
					Time:  delApptEnd,
					Valid: true,
				},
				ApptNote: "",
				Timezone: "America/Chicago",
			},
			Carrier: models.Carrier{
				RateConfirmationSent: true,
				Name:                 "F2F TRANSPORT LLC",
				Email:                "<EMAIL>",
			},
		},
	}

	assert.Equal(t, expectedLoad, loadData)
}

func TestParseApptTime(t *testing.T) {
	nyLoc, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	chicagoLoc, err := time.LoadLocation("America/Chicago")
	require.NoError(t, err)

	tests := []struct {
		name     string
		html     string
		timeZone *time.Location
		expected models.NullTime
		wantErr  bool
	}{
		{
			name:     "date with slashes and year",
			html:     `<div class="stop-appt">5/12/2025 08:00</div>`,
			timeZone: nyLoc,
			expected: models.ToValidNullTime(time.Date(2025, 5, 12, 8, 0, 0, 0, nyLoc)),
			wantErr:  false,
		},
		{
			name:     "date with slashes and no year",
			html:     `<div class="stop-appt">5/12 08:00</div>`,
			timeZone: nyLoc,
			expected: models.ToValidNullTime(time.Date(2025, 5, 12, 8, 0, 0, 0, nyLoc)),
			wantErr:  false,
		},
		{
			name:     "datetime with hyphens",
			html:     `<div class="stop-appt">2025-05-12 08:00</div>`,
			timeZone: nyLoc,
			expected: models.ToValidNullTime(time.Date(2025, 5, 12, 8, 0, 0, 0, nyLoc)),
			wantErr:  false,
		},
		{
			name:     "date-only with hyphens",
			html:     `<div class="stop-appt">2025-05-12</div>`,
			timeZone: chicagoLoc,
			expected: models.ToValidNullTime(time.Date(2025, 5, 12, 0, 0, 0, 0, chicagoLoc)),
			wantErr:  false,
		},
		{
			// On Load Board, stop-appt and stop-appt-2 (if present) are siblings
			// On Tracking Page, stop-appt-2 is a child of stop-appt
			// This means selection.Find(class).Text() returns "5/12/2025 08:00\n\n\t5/12/2025 16:00",
			// causing the switch-case to return false for strings.Count(apptString, "/") == 2 condition
			// and always land on dateTimeSlashesNoYearLayout
			name:     "simulate 2 children divs with dates",
			html:     `<div class="stop-appt">5/12/2025 08:00<div class="stop-appt-2">5/13/2025 08:00</div></div>`,
			timeZone: nyLoc,
			expected: models.ToValidNullTime(time.Date(2025, 5, 12, 8, 0, 0, 0, nyLoc)),
			wantErr:  false,
		},
		{
			name:     "empty string",
			html:     `<div class="stop-appt"></div>`,
			timeZone: nyLoc,
			expected: models.NullTime{},
			wantErr:  false,
		},
		{
			name:     "whitespace only",
			html:     `<div class="stop-appt">   </div>`,
			timeZone: nyLoc,
			expected: models.NullTime{},
			wantErr:  false,
		},
		{
			name:     "with newlines",
			html:     `<div class="stop-appt">5/12/2025 08:00\n</div>`,
			timeZone: nyLoc,
			expected: models.ToValidNullTime(time.Date(2025, 5, 12, 8, 0, 0, 0, nyLoc)),
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			doc, err := goquery.NewDocumentFromReader(strings.NewReader(tt.html))
			require.NoError(t, err)

			result, err := parseApptTime(doc.Selection, ".stop-appt", tt.timeZone)

			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.expected.Valid, result.Valid)
			if tt.expected.Valid {
				// Convert both times to UTC for comparison
				expectedUTC := tt.expected.Time.In(time.UTC)
				resultUTC := result.Time.In(time.UTC)
				assert.Equal(t, expectedUTC, resultUTC)
			}
		})
	}
}
