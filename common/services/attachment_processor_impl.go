package services

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"golang.org/x/sync/semaphore"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/s3fetcher"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
)

// AttachmentProcessorServiceImpl implements AttachmentProcessorService
type AttachmentProcessorServiceImpl struct {
	db            *gorm.DB
	s3Client      s3fetcher.Fetcher
	openaiService openai.Service
}

// Constants for parallel processing
const (
	// maxConcurrentDownloads limits concurrent downloads to prevent memory exhaustion in Lambda
	maxConcurrentDownloads = 2

	// maxSingleFileSizeMB is the maximum size for a single file before falling back to sequential
	maxSingleFileSizeMB = 25

	// maxTotalSizeMB is the maximum total size before falling back to sequential
	maxTotalSizeMB = 50

	// bytesPerMB is the number of bytes in one megabyte
	bytesPerMB = 1024 * 1024
)

// attachmentJob represents an attachment with its file size for processing
type attachmentJob struct {
	pa       *models.ProcessedAttachment
	fileSize int64 // bytes
}

// NewAttachmentProcessorService creates a new instance of AttachmentProcessorService
func NewAttachmentProcessorService(
	ctx context.Context,
	db *gorm.DB,
	s3Client s3fetcher.Fetcher,
	openaiService openai.Service,
) (AttachmentProcessorService, error) {
	if s3Client == nil {
		var err error
		s3Client, err = s3fetcher.New(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to create S3 client: %w", err)
		}
	}

	if openaiService == nil {
		var err error
		openaiService, err = openai.NewService(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to create OpenAI service: %w", err)
		}
	}

	return &AttachmentProcessorServiceImpl{
		db:            db,
		s3Client:      s3Client,
		openaiService: openaiService,
	}, nil
}

// GetMarkdownIfReady returns the processed markdown if ready (non-blocking)
// Queries by RFCMessageID + OriginalFileName (our deduplication key)
func (s *AttachmentProcessorServiceImpl) GetMarkdownIfReady(
	ctx context.Context,
	emailID uint,
	attachmentExternalID string,
) (string, error) {
	// Get the email to find the attachment and RFCMessageID
	var email models.Email
	if err := s.db.WithContext(ctx).First(&email, emailID).Error; err != nil {
		return "", fmt.Errorf("failed to get email: %w", err)
	}

	// Find the attachment by external ID to get its filename
	var attachment *models.Attachment
	for i := range email.Attachments {
		if email.Attachments[i].ExternalID == attachmentExternalID {
			attachment = &email.Attachments[i]
			break
		}
	}

	if attachment == nil {
		return "", gorm.ErrRecordNotFound
	}

	// Query by RFCMessageID + OriginalFileName (our deduplication key)
	pa, found, err := emailDB.GetProcessedAttachmentByFilename(
		ctx,
		email.ServiceID,
		email.RFCMessageID,
		attachment.OriginalFileName,
		StatusSuccess,
	)
	if err != nil {
		return "", fmt.Errorf("failed to query attachment: %w", err)
	}

	if !found {
		return "", gorm.ErrRecordNotFound
	}

	// Decompress markdown
	decompressed, err := helpers.DecompressGzip(pa.MarkdownContent)
	if err != nil {
		return "", fmt.Errorf("failed to decompress markdown: %w", err)
	}

	return decompressed, nil
}

// GetMarkdownOrWait returns markdown, waiting up to maxWait for processing
func (s *AttachmentProcessorServiceImpl) GetMarkdownOrWait(
	ctx context.Context,
	emailID uint,
	attachmentExternalID string,
	maxWait time.Duration,
) (string, bool, error) {
	deadline := time.Now().Add(maxWait)
	pollInterval := 50 * time.Millisecond
	maxPollInterval := 1 * time.Second

	for {
		// Try to get markdown
		markdown, err := s.GetMarkdownIfReady(ctx, emailID, attachmentExternalID)
		if err == nil {
			return markdown, true, nil
		}
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return "", false, err
		}

		// Check if processing failed
		status, errorMsg, err := s.GetStatus(ctx, emailID, attachmentExternalID)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return "", false, err
			}
			// Not found yet, continue polling
		} else if status == StatusFailed {
			return "", false, fmt.Errorf("attachment processing failed: %s", errorMsg)
		}

		// Check timeout
		if time.Now().After(deadline) {
			return "", false, errors.New("timeout waiting for attachment processing")
		}

		// Wait with exponential backoff before retrying
		select {
		case <-ctx.Done():
			return "", false, ctx.Err()
		case <-time.After(pollInterval):
			// Double the interval for next iteration, capped at max
			if pollInterval < maxPollInterval {
				pollInterval = time.Duration(float64(pollInterval) * 1.5)
				if pollInterval > maxPollInterval {
					pollInterval = maxPollInterval
				}
			}
		}
	}
}

// GetStatus returns the current processing status of an attachment
// Queries by RFCMessageID + OriginalFileName (our deduplication key)
func (s *AttachmentProcessorServiceImpl) GetStatus(
	ctx context.Context,
	emailID uint,
	attachmentExternalID string,
) (status string, errorMsg string, err error) {
	// Get the email to find the attachment and RFCMessageID
	var email models.Email
	if err := s.db.WithContext(ctx).First(&email, emailID).Error; err != nil {
		return "", "", fmt.Errorf("failed to get email: %w", err)
	}

	// Find the attachment by external ID to get its filename
	var attachment *models.Attachment
	for i := range email.Attachments {
		if email.Attachments[i].ExternalID == attachmentExternalID {
			attachment = &email.Attachments[i]
			break
		}
	}

	if attachment == nil {
		return "", "", gorm.ErrRecordNotFound
	}

	// Query by RFCMessageID + OriginalFileName (our deduplication key)
	pa := &models.ProcessedAttachment{}
	result := s.db.WithContext(ctx).
		Where("service_id = ? AND rfc_message_id = ? AND original_file_name = ?",
			email.ServiceID, email.RFCMessageID, attachment.OriginalFileName).
		Take(pa)

	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return "", "", gorm.ErrRecordNotFound
	}

	if result.Error != nil {
		return "", "", fmt.Errorf("failed to query attachment status: %w", result.Error)
	}

	return pa.ProcessingStatus, pa.ErrorMessage, nil
}

// GetAllMarkdownForEmail returns all processed markdown for an email
// Queries by RFCMessageID + OriginalFileName (our deduplication key)
func (s *AttachmentProcessorServiceImpl) GetAllMarkdownForEmail(
	ctx context.Context,
	emailID uint,
) (map[string]string, int, int, error) {
	// Get the email to access its attachments and RFCMessageID
	var email models.Email
	if err := s.db.WithContext(ctx).First(&email, emailID).Error; err != nil {
		return nil, 0, 0, fmt.Errorf("failed to get email: %w", err)
	}

	if len(email.Attachments) == 0 {
		return make(map[string]string), 0, 0, nil
	}

	// Build map of filename -> attachment for lookup
	filenameToAttachment := make(map[string]models.Attachment)
	var filenames []string
	for _, att := range email.Attachments {
		filenameToAttachment[att.OriginalFileName] = att
		filenames = append(filenames, att.OriginalFileName)
	}

	// Query all processed attachments by RFCMessageID + filenames
	var processedAttachments []models.ProcessedAttachment
	if err := s.db.WithContext(ctx).
		Where("service_id = ? AND rfc_message_id = ? AND original_file_name IN ? AND processing_status = ?",
			email.ServiceID, email.RFCMessageID, filenames, StatusSuccess).
		Find(&processedAttachments).Error; err != nil {
		return nil, 0, 0, fmt.Errorf("failed to query processed attachments: %w", err)
	}

	// Build result map: attachment_external_id -> markdown
	result := make(map[string]string)
	pendingCount := 0
	failedCount := 0

	for _, pa := range processedAttachments {
		// Find the attachment that matches this filename
		att, ok := filenameToAttachment[pa.OriginalFileName]
		if !ok {
			continue
		}

		decompressed, err := helpers.DecompressGzip(pa.MarkdownContent)
		if err == nil {
			result[att.ExternalID] = decompressed
		}
	}

	// Count pending/failed by checking if any filenames don't have successful records
	processedFilenames := make(map[string]bool)
	for _, pa := range processedAttachments {
		processedFilenames[pa.OriginalFileName] = true
	}

	for _, att := range email.Attachments {
		if !processedFilenames[att.OriginalFileName] {
			// Check if it's pending or failed
			var pa models.ProcessedAttachment
			if err := s.db.WithContext(ctx).
				Where("service_id = ? AND rfc_message_id = ? AND original_file_name = ?",
					email.ServiceID, email.RFCMessageID, att.OriginalFileName).
				First(&pa).Error; err == nil {
				switch pa.ProcessingStatus {
				case StatusPending:
					pendingCount++
				case StatusProcessing:
					pendingCount++ // Count as pending since it's not ready yet
				case StatusFailed:
					failedCount++
				}
			}
		}
	}

	return result, pendingCount, failedCount, nil
}

// processSingleAttachment processes a single attachment: downloads, converts, compresses, and saves.
// Thread-safe: uses mutex for successCount/failureCount updates.
func (s *AttachmentProcessorServiceImpl) processSingleAttachment(
	ctx context.Context,
	pa *models.ProcessedAttachment,
	successCount *int,
	failureCount *int,
	mu *sync.Mutex,
) {
	attachmentStartTime := time.Now()

	// Update status to processing
	result := s.db.WithContext(ctx).Model(pa).
		Where("processing_status = ?", StatusPending).
		Update("processing_status", StatusProcessing)

	if result.Error != nil {
		if errFail := s.handleProcessingFailure(ctx, pa.ID, result.Error.Error()); errFail != nil {
			log.Error(ctx, "failed to update attachment status to processing", zap.Error(errFail))
		}
		log.WarnNoSentry(ctx, "failed to mark attachment as processing",
			zap.Uint("attachmentID", pa.ID),
			zap.String("filename", pa.OriginalFileName),
			zap.Error(result.Error),
		)
		mu.Lock()
		*failureCount++
		mu.Unlock()
		return
	}

	// Check if update actually affected a row (prevents duplicate processing in race conditions)
	// If RowsAffected == 0, another goroutine already claimed this attachment for processing
	if result.RowsAffected == 0 {
		log.Info(ctx, "attachment already claimed for processing by another goroutine, skipping",
			zap.Uint("attachmentID", pa.ID),
			zap.String("filename", pa.OriginalFileName),
			zap.String("rfcMessageID", pa.RFCMessageID),
		)
		return
	}

	// Download from S3
	downloadStartTime := time.Now()
	fileData, err := s.s3Client.FetchObjectFromS3(ctx, pa.S3URL)
	downloadDuration := time.Since(downloadStartTime)

	if err != nil {
		if errFail := s.handleProcessingFailure(
			ctx,
			pa.ID,
			fmt.Sprintf("failed to download from S3: %v", err),
		); errFail != nil {
			log.Error(ctx, "failed to download from S3", zap.Error(err))
		}
		log.WarnNoSentry(ctx, "S3 download failed",
			zap.Uint("attachmentID", pa.ID),
			zap.String("filename", pa.OriginalFileName),
			zap.String("s3url", pa.S3URL),
			zap.Duration("duration", downloadDuration),
			zap.Error(err),
		)
		mu.Lock()
		*failureCount++
		mu.Unlock()
		return
	}

	log.Debug(ctx, "S3 download completed",
		zap.Uint("attachmentID", pa.ID),
		zap.String("filename", pa.OriginalFileName),
		zap.Int("fileSizeBytes", len(fileData)),
		zap.Duration("duration", downloadDuration),
	)

	// Convert PDF to markdown
	conversionStartTime := time.Now()
	// Construct Attachment from ProcessedAttachment for the conversion function
	attachment := &models.Attachment{
		MessageExternalID: pa.MessageExternalID,
		ExternalID:        pa.AttachmentExternalID,
		OriginalFileName:  pa.OriginalFileName,
		S3URL:             pa.S3URL,
		MimeType:          "application/pdf", // This service only processes PDFs
	}
	markdown, err := s.convertToMarkdown(ctx, fileData, pa.OriginalFileName, attachment)
	conversionDuration := time.Since(conversionStartTime)

	if err != nil {
		if errFail := s.handleProcessingFailure(
			ctx,
			pa.ID,
			fmt.Sprintf("failed to convert to markdown: %v", err),
		); errFail != nil {
			log.Error(ctx, "failed to convert to markdown", zap.Error(err))
		}
		log.Warn(ctx, "PDF conversion failed",
			zap.Uint("attachmentID", pa.ID),
			zap.String("filename", pa.OriginalFileName),
			zap.Duration("duration", conversionDuration),
			zap.Error(err),
		)
		mu.Lock()
		*failureCount++
		mu.Unlock()
		return
	}

	log.Debug(ctx, "PDF conversion completed",
		zap.Uint("attachmentID", pa.ID),
		zap.String("filename", pa.OriginalFileName),
		zap.Int("markdownSizeBytes", len(markdown)),
		zap.Duration("duration", conversionDuration),
	)

	if strings.TrimSpace(markdown) == "" {
		msg := "conversion resulted in empty markdown"
		if errFail := s.handleProcessingFailure(ctx, pa.ID, msg); errFail != nil {
			log.Error(ctx, "conversion resulted in empty markdown", zap.Error(err))
		}
		log.WarnNoSentry(ctx, "empty markdown after conversion",
			zap.Uint("attachmentID", pa.ID),
			zap.String("filename", pa.OriginalFileName),
		)
		mu.Lock()
		*failureCount++
		mu.Unlock()
		return
	}

	// Compress markdown
	compressionStartTime := time.Now()
	compressed, err := helpers.CompressGzip(markdown)
	compressionDuration := time.Since(compressionStartTime)

	if err != nil {
		if errFail := s.handleProcessingFailure(
			ctx,
			pa.ID,
			fmt.Sprintf("failed to compress markdown: %v", err),
		); errFail != nil {
			log.Error(ctx, "failed to compress markdown", zap.Error(err))
		}
		log.Warn(ctx, "markdown compression failed",
			zap.Uint("attachmentID", pa.ID),
			zap.String("filename", pa.OriginalFileName),
			zap.Int("originalSize", len(markdown)),
			zap.Duration("duration", compressionDuration),
			zap.Error(err),
		)
		mu.Lock()
		*failureCount++
		mu.Unlock()
		return
	}

	compressionRatio := float64(len(compressed)) / float64(len(markdown))
	log.Debug(ctx, "markdown compression completed",
		zap.Uint("attachmentID", pa.ID),
		zap.String("filename", pa.OriginalFileName),
		zap.Int("originalSize", len(markdown)),
		zap.Int("compressedSize", len(compressed)),
		zap.Float64("compressionRatio", compressionRatio),
		zap.Duration("duration", compressionDuration),
	)

	// Update database with success
	dbStartTime := time.Now()
	updates := map[string]any{
		"processing_status": StatusSuccess,
		"markdown_content":  compressed,
	}

	if err := s.db.WithContext(ctx).Model(pa).Updates(updates).Error; err != nil {
		if errFail := s.handleProcessingFailure(
			ctx,
			pa.ID,
			fmt.Sprintf("failed to update attachment: %v", err),
		); errFail != nil {
			log.Error(ctx, "failed to update attachment", zap.Error(err))
		}
		log.WarnNoSentry(ctx, "failed to save attachment to RDS",
			zap.Uint("attachmentID", pa.ID),
			zap.String("filename", pa.OriginalFileName),
			zap.Duration("duration", time.Since(dbStartTime)),
			zap.Error(err),
		)
		mu.Lock()
		*failureCount++
		mu.Unlock()
		return
	}

	attachmentDuration := time.Since(attachmentStartTime)
	log.Info(ctx, "attachment processed successfully",
		zap.Uint("attachmentID", pa.ID),
		zap.String("filename", pa.OriginalFileName),
		zap.Int("fileSizeBytes", len(fileData)),
		zap.Int("markdownSizeBytes", len(markdown)),
		zap.Int("compressedSizeBytes", len(compressed)),
		zap.Float64("compressionRatio", compressionRatio),
		zap.Duration("downloadDuration", downloadDuration),
		zap.Duration("conversionDuration", conversionDuration),
		zap.Duration("compressionDuration", compressionDuration),
		zap.Duration("dbDuration", time.Since(dbStartTime)),
		zap.Duration("totalDuration", attachmentDuration),
	)

	mu.Lock()
	*successCount++
	mu.Unlock()
}

// ProcessEmailAttachments processes all attachments for an email.
// Deduplicates by RFCMessageID + OriginalFileName (same for all recipients).
func (s *AttachmentProcessorServiceImpl) ProcessEmailAttachments(
	ctx context.Context,
	emailID uint,
	threadID string,
	serviceID uint,
	rfcMessageID string,
	attachments []models.Attachment,
) error {
	if strings.TrimSpace(rfcMessageID) == "" {
		return errors.New("rfcMessageID cannot be empty: required for deduplication across recipients")
	}

	startTime := time.Now()
	var successCount, failureCount int

	// Check for existing processed attachments by RFCMessageID + OriginalFileName
	// RFCMessageID is the same for all recipients, so this deduplicates across users
	var toProcess []models.ProcessedAttachment   // Attachments that need actual processing
	var alreadyProcessed = make(map[string]bool) // Track which attachments already exist (by filename)
	var toRetry []string                         // Filenames of failed attachments that need retry

	for _, attachment := range attachments {
		// Check if this attachment has already been processed successfully
		var existing models.ProcessedAttachment
		err := s.db.WithContext(ctx).
			Where("service_id = ? AND rfc_message_id = ? AND original_file_name = ?",
				serviceID, rfcMessageID, attachment.OriginalFileName).
			First(&existing).Error

		switch {
		case err == nil:
			// Found existing record - check its status
			switch existing.ProcessingStatus {
			case StatusSuccess:
				// Already processed successfully - skip
				alreadyProcessed[attachment.OriginalFileName] = true
				log.Debug(ctx, "skipping already processed attachment",
					zap.String("rfcMessageID", rfcMessageID),
					zap.String("filename", attachment.OriginalFileName),
					zap.Uint("existingEmailID", existing.EmailID),
					zap.Uint("currentEmailID", emailID),
				)
			case StatusPending, StatusProcessing:
				// Already queued or processing - skip (will be processed by another goroutine or already in progress)
				log.Debug(ctx, "skipping attachment already queued/processing",
					zap.String("rfcMessageID", rfcMessageID),
					zap.String("filename", attachment.OriginalFileName),
					zap.String("status", existing.ProcessingStatus),
				)
			case StatusFailed:
				// Failed attachment - mark for retry (will be updated separately, not inserted)
				toRetry = append(toRetry, attachment.OriginalFileName)
				log.Info(ctx, "retrying previously failed attachment",
					zap.String("rfcMessageID", rfcMessageID),
					zap.String("filename", attachment.OriginalFileName),
					zap.String("previousError", existing.ErrorMessage),
				)
				// Don't add to toProcess - we'll update it directly to StatusPending
			}
		case errors.Is(err, gorm.ErrRecordNotFound):
			// No existing record - create new one for processing
			pa := models.ProcessedAttachment{
				EmailID:              emailID,
				ThreadID:             threadID,
				ServiceID:            serviceID,
				RFCMessageID:         rfcMessageID,
				AttachmentExternalID: attachment.ExternalID,
				MessageExternalID:    attachment.MessageExternalID,
				OriginalFileName:     attachment.OriginalFileName,
				S3URL:                attachment.S3URL,
				ProcessingStatus:     StatusPending,
			}
			toProcess = append(toProcess, pa)
		default:
			// Database error
			log.Warn(ctx, "failed to check for existing attachment",
				zap.String("rfcMessageID", rfcMessageID),
				zap.String("filename", attachment.OriginalFileName),
				zap.Error(err),
			)
			// Still queue it for processing (fail-open)
			pa := models.ProcessedAttachment{
				EmailID:              emailID,
				ThreadID:             threadID,
				ServiceID:            serviceID,
				RFCMessageID:         rfcMessageID,
				AttachmentExternalID: attachment.ExternalID,
				MessageExternalID:    attachment.MessageExternalID,
				OriginalFileName:     attachment.OriginalFileName,
				S3URL:                attachment.S3URL,
				ProcessingStatus:     StatusPending,
			}
			toProcess = append(toProcess, pa)
		}
	}

	// First, update any failed records to StatusPending for retry
	if len(toRetry) > 0 {
		if err := s.db.WithContext(ctx).
			Model(&models.ProcessedAttachment{}).
			Where("service_id = ? AND rfc_message_id = ? AND original_file_name IN ? AND processing_status = ?",
				serviceID, rfcMessageID, toRetry, StatusFailed).
			Updates(map[string]any{
				"processing_status": StatusPending,
				"error_message":     nil, // Clear previous error message
			}).Error; err != nil {
			log.Warn(ctx, "failed to reset failed attachments for retry",
				zap.Uint("emailID", emailID),
				zap.Int("count", len(toRetry)),
				zap.Error(err),
			)
			// Continue processing - don't fail the entire operation
		} else {
			log.Info(ctx, "reset failed attachments for retry",
				zap.Uint("emailID", emailID),
				zap.Int("count", len(toRetry)),
			)
		}
	}

	// Insert new records (failed records already updated above, so OnConflict DoNothing is safe)
	if len(toProcess) > 0 {
		if err := s.db.WithContext(ctx).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{
					{Name: "service_id"},
					{Name: "rfc_message_id"},
					{Name: "original_file_name"},
				},
				DoNothing: true, // If already exists (pending/processing), skip
			}).CreateInBatches(toProcess, 50).Error; err != nil {
			log.Error(ctx, "failed to batch queue attachments",
				zap.Uint("emailID", emailID),
				zap.Int("count", len(toProcess)),
				zap.Error(err),
			)
			return fmt.Errorf("failed to batch queue attachments: %w", err)
		}
	}

	// Reload records to get IDs after batch insert/update (for processing)
	// Include both new records and retried failed records
	var toProcessWithIDs []models.ProcessedAttachment
	allFilenames := make([]string, 0, len(toProcess)+len(toRetry))
	for _, pa := range toProcess {
		allFilenames = append(allFilenames, pa.OriginalFileName)
	}
	allFilenames = append(allFilenames, toRetry...)

	if len(allFilenames) > 0 {
		if err := s.db.WithContext(ctx).
			Where("service_id = ? AND rfc_message_id = ? AND original_file_name IN ? AND processing_status = ?",
				serviceID, rfcMessageID, allFilenames, StatusPending).
			Find(&toProcessWithIDs).Error; err != nil {
			log.Error(ctx, "failed to reload queued attachments",
				zap.Uint("emailID", emailID),
				zap.Error(err),
			)
			return fmt.Errorf("failed to reload queued attachments: %w", err)
		}
	}

	log.Info(ctx, "starting attachment processing for email",
		zap.Uint("emailID", emailID),
		zap.String("rfcMessageID", rfcMessageID),
		zap.Int("totalAttachments", len(attachments)),
		zap.Int("newAttachments", len(toProcess)),
		zap.Int("retriedAttachments", len(toRetry)),
		zap.Int("alreadyProcessed", len(alreadyProcessed)),
	)

	// If all attachments were reused or already processing, we're done
	if len(toProcessWithIDs) == 0 {
		successCount = len(alreadyProcessed) // Update count to reflect reused attachments
		totalDuration := time.Since(startTime)
		log.Info(ctx, "attachment processing for email completed successfully (all reused)",
			zap.Uint("emailID", emailID),
			zap.String("rfcMessageID", rfcMessageID),
			zap.Int("successCount", successCount),
			zap.Int("totalCount", len(attachments)),
			zap.Duration("totalDuration", totalDuration),
		)
		return nil
	}

	// Get file sizes for size-aware parallelization
	jobs := make([]attachmentJob, 0, len(toProcessWithIDs))
	var totalSize int64
	maxSingleSize := int64(0)

	for i := range toProcessWithIDs {
		pa := &toProcessWithIDs[i]
		size, err := s.s3Client.GetObjectSize(ctx, pa.S3URL)
		if err != nil {
			// If we can't get size, use a conservative default (10MB)
			// This will cause us to fall back to sequential processing
			log.Warn(ctx, "failed to get file size, using default",
				zap.Uint("attachmentID", pa.ID),
				zap.String("filename", pa.OriginalFileName),
				zap.Error(err),
			)
			size = 10 * bytesPerMB // 10MB default
		}

		if size > maxSingleSize {
			maxSingleSize = size
		}
		totalSize += size

		jobs = append(jobs, attachmentJob{
			pa:       pa,
			fileSize: size,
		})
	}

	// Sort by file size (largest first) for better time distribution
	sort.Slice(jobs, func(i, j int) bool {
		return jobs[i].fileSize > jobs[j].fileSize
	})

	// Decide whether to parallelize based on sizes
	maxSingleSizeMB := float64(maxSingleSize) / float64(bytesPerMB)
	totalSizeMB := float64(totalSize) / float64(bytesPerMB)
	shouldParallelize := len(jobs) > 1 &&
		maxSingleSizeMB <= maxSingleFileSizeMB &&
		totalSizeMB <= maxTotalSizeMB &&
		len(jobs) <= 10 // Reasonable limit even for small files

	if shouldParallelize {
		log.Info(ctx, "processing attachments in parallel",
			zap.Uint("emailID", emailID),
			zap.Int("count", len(jobs)),
			zap.Float64("totalSizeMB", totalSizeMB),
			zap.Float64("maxSingleSizeMB", maxSingleSizeMB),
			zap.Int("maxConcurrent", maxConcurrentDownloads),
		)

		// Process with semaphore-controlled concurrency
		sem := semaphore.NewWeighted(int64(maxConcurrentDownloads))
		var wg sync.WaitGroup
		var mu sync.Mutex

		for _, job := range jobs {
			wg.Add(1)
			go func(j attachmentJob) {
				defer wg.Done()

				// Acquire semaphore
				if err := sem.Acquire(ctx, 1); err != nil {
					log.Warn(ctx, "failed to acquire semaphore",
						zap.Uint("attachmentID", j.pa.ID),
						zap.Error(err),
					)
					mu.Lock()
					failureCount++
					mu.Unlock()
					return
				}
				defer sem.Release(1)

				s.processSingleAttachment(ctx, j.pa, &successCount, &failureCount, &mu)
			}(job)
		}

		wg.Wait()
	} else {
		// Sequential processing for safety
		log.Info(ctx, "processing attachments sequentially",
			zap.Uint("emailID", emailID),
			zap.Int("count", len(jobs)),
			zap.Float64("totalSizeMB", totalSizeMB),
			zap.Float64("maxSingleSizeMB", maxSingleSizeMB),
			zap.String("reason", func() string {
				if len(jobs) <= 1 {
					return "single attachment"
				}
				if maxSingleSizeMB > maxSingleFileSizeMB {
					return "single file too large"
				}
				if totalSizeMB > maxTotalSizeMB {
					return "total size too large"
				}
				return "too many attachments"
			}()),
		)

		var mu sync.Mutex
		for _, job := range jobs {
			s.processSingleAttachment(ctx, job.pa, &successCount, &failureCount, &mu)
		}
	}

	totalDuration := time.Since(startTime)

	// Log completion with appropriate level based on results
	if failureCount > 0 {
		log.Warn(ctx, "attachment processing for email completed with some failures",
			zap.Uint("emailID", emailID),
			zap.Int("successCount", successCount),
			zap.Int("failureCount", failureCount),
			zap.Int("totalCount", len(attachments)),
			zap.Duration("totalDuration", totalDuration),
		)
	} else {
		log.Info(ctx, "attachment processing for email completed successfully",
			zap.Uint("emailID", emailID),
			zap.Int("successCount", successCount),
			zap.Int("totalCount", len(attachments)),
			zap.Duration("totalDuration", totalDuration),
		)
	}

	// Fail-open: return nil even if some attachments failed
	// Only return error if batch insert failed completely
	return nil
}

// convertToMarkdown converts PDF file data to markdown using available converters.
func (s *AttachmentProcessorServiceImpl) convertToMarkdown(
	ctx context.Context,
	fileData []byte,
	fileName string,
	attachment *models.Attachment,
) (string, error) {
	// Use the extractor's PDFToMarkdownWithFallbacks which handles PDF to markdown conversion
	// with multiple fallback methods
	markdown, err := extractor.PDFToMarkdownWithFallbacks(ctx, s.openaiService, fileData, fileName, attachment)
	if err != nil {
		return "", err
	}
	return markdown, nil
}

// handleProcessingFailure marks an attachment as failed.
// Internal method used by ProcessEmailAttachments for error handling.
func (s *AttachmentProcessorServiceImpl) handleProcessingFailure(
	ctx context.Context,
	processorID uint,
	errorMessage string,
) error {
	return s.db.WithContext(ctx).
		Model(&models.ProcessedAttachment{}).
		Where("id = ?", processorID).
		Updates(map[string]any{
			"processing_status": StatusFailed,
			"error_message":     errorMessage,
		}).
		Error
}
