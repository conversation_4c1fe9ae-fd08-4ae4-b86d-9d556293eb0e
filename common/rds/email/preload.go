package email

import (
	"context"

	email_helpers "github.com/drumkitai/drumkit/common/helpers/emails"
	"github.com/drumkitai/drumkit/common/models"
)

// PreloadProcessedAttachments populates email.ProcessedAttachments by querying
// ProcessedAttachments using RFCMessageID + OriginalFileName (not EmailID, due to deduplication).
// This allows consumers to iterate through email.ProcessedAttachments
func PreloadProcessedAttachments(ctx context.Context, email *models.Email) error {
	if len(email.Attachments) == 0 {
		email.ProcessedAttachments = []models.ProcessedAttachment{}
		return nil
	}

	// Build list of filenames to query
	var filenames []string
	for _, att := range email.Attachments {
		if email_helpers.IsPDF(att.MimeType, att.OriginalFileName) {
			filenames = append(filenames, att.OriginalFileName)
		}
	}

	// Query ProcessedAttachments by RFCMessageID + OriginalFileName (our deduplication key)
	processedAttachments, err := GetProcessedAttachmentsByFilenames(
		ctx,
		email.ServiceID,
		email.RFCMessageID,
		filenames,
		"success",
	)
	if err != nil {
		return err
	}

	email.ProcessedAttachments = processedAttachments
	return nil
}
