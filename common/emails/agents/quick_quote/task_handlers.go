package quickquoteagent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
)

// handlePricingLookup executes the pricing lookup task.
// It fetches quotes, selects one based on strategy, applies margin/guardrails,
// and stores the result in the execution context for subsequent tasks.
// Returns task metadata for invocation tracking.
func handlePricingLookup(
	ctx context.Context,
	execCtx *TaskExecutionContext,
	task *agentmodels.QuickQuoteAgentTask,
) (*agentmodels.TaskInvocationMetadata, error) {

	ctx, span := otel.StartSpan(ctx, "agent.handlePricingLookup", nil)
	var err error
	defer func() { span.End(err) }()

	log.Info(
		ctx,
		"Quick Quote Agent: executing pricing lookup task",
		zap.Uint("taskID", task.ID),
		zap.Uint("agentID", execCtx.Agent.ID),
	)

	// Extract lane info from quote request
	suggestedRequest := execCtx.QuoteRequest.SuggestedRequest
	stops := buildStopsFromQuoteRequest(suggestedRequest)

	// If suggested request doesn't have stops, try applied request
	if len(stops) < 2 {
		appliedRequest := execCtx.QuoteRequest.AppliedRequest
		stops = buildStopsFromQuoteRequest(appliedRequest)
		log.Info(
			ctx,
			"falling back to applied request for stops",
			zap.Int("numStopsFromApplied", len(stops)),
		)
	}

	if len(stops) < 2 {
		err = fmt.Errorf("insufficient stop data in quote request: found %d stops, need at least 2", len(stops))
		log.Error(ctx, "insufficient stops", zap.Error(err))
		return nil, err
	}

	// Determine transport type - use override if configured, otherwise use suggested
	transportType := suggestedRequest.TransportType
	if execCtx.Agent.Config.TransportTypeOverride != "" {
		transportType = execCtx.Agent.Config.TransportTypeOverride
		log.Info(
			ctx,
			"using transport type override from agent config",
			zap.String("transportType", string(transportType)),
		)
		// Update suggested_request with override in DB so metrics are accurate and user re-runs use correct type
		err := quoteRequestDB.UpdateQuoteRequestWithSuggestedTransportType(
			ctx,
			execCtx.QuoteRequest.ID,
			transportType,
		)
		if err != nil {
			log.Warn(
				ctx,
				"failed to update quote request with agent config transport type override",
				zap.Error(err),
				zap.Uint("quoteRequestID", execCtx.QuoteRequest.ID),
			)
			// Don't fail the task if DB update fails - we'll still use the override for pricing
		}
	}

	// Determine dates - default to tomorrow/day-after if not specified
	// If pickup date isn't provided, set it to tomorrow
	pickupDate := time.Now().AddDate(0, 0, 1)
	if suggestedRequest.PickupDate.Valid {
		pickupDate = suggestedRequest.PickupDate.Time
	}

	// If delivery date isn't provided, set it to:
	// - pickup date + 1 day if pickup date is provided
	// - today + 2 days otherwise
	deliveryDate := time.Now().AddDate(0, 0, 2)
	if suggestedRequest.DeliveryDate.Valid {
		deliveryDate = suggestedRequest.DeliveryDate.Time
	} else if suggestedRequest.PickupDate.Valid {
		// Pickup is provided but delivery isn't - set delivery to pickup + 1 day
		deliveryDate = pickupDate.AddDate(0, 0, 1)
	}

	pricingInput := PricingLookupInput{
		Service:        *execCtx.Service,
		User:           execCtx.User,
		Stops:          stops,
		TransportType:  transportType,
		PickupDate:     pickupDate,
		DeliveryDate:   deliveryDate,
		QuoteRequestID: execCtx.QuoteRequest.ID,
		EmailID:        execCtx.Email.ID,
		ThreadID:       execCtx.Email.ThreadID,
	}

	pricingResult, err := GetQuotesForAgent(ctx, pricingInput)
	if err != nil {
		log.Error(ctx, "failed to get quotes from pricing integrations", zap.Error(err))
		return nil, err
	}

	if len(pricingResult.Quotes) == 0 {
		log.Warn(
			ctx,
			"no quotes returned from any pricing integration",
			zap.Int("numErrors", len(pricingResult.QuoteErrors)),
		)
		return nil, ErrNoQuotesFound
	}

	log.Info(
		ctx,
		"received quotes from pricing integrations",
		zap.Int("numQuotes", len(pricingResult.Quotes)),
		zap.Int("numErrors", len(pricingResult.QuoteErrors)),
	)

	// Filter quotes with valid rates and log warnings for invalid ones
	var validQuotes []AgentQuote
	for _, q := range pricingResult.Quotes {
		if q.Rates.Target <= 0 {
			log.Warn(
				ctx,
				"Quick Quote Agent: skipping quote with invalid rate",
				zap.String("source", string(q.Source)),
				zap.String("type", string(q.Type)),
				zap.Float64("targetRate", q.Rates.Target),
			)
			continue
		}
		validQuotes = append(validQuotes, q)
	}

	if len(validQuotes) == 0 {
		log.Error(
			ctx,
			"no valid quotes with positive rates found",
			zap.Int("totalQuotes", len(pricingResult.Quotes)),
		)
		return nil, ErrNoQuotesFound
	}

	// TODO: (FUTURE): Select quote based on strategy (from task metadata) task meta data for pricing lookup can
	// override agent specific config.
	// strategy := execCtx.Agent.Config.QuoteSelectionStrategy
	// if task.Metadata.PricingLookupTask != nil &&
	// 	task.Metadata.PricingLookupTask.QuoteSelectionStrategy != "" {
	// 	strategy = task.Metadata.PricingLookupTask.QuoteSelectionStrategy
	// 	log.Info(
	// 		ctx,
	// 		"using task-specific quote selection strategy",
	// 		zap.String("strategy", string(strategy)),
	// 	)
	// }

	// For now, we use the agent config for quote selection. In the future, task metadata could override specific
	// selection parameters (useful if we have branching logic different tasks same agent for diff customers/users etc)
	selectedQuote, err := selectQuoteByStrategy(ctx, validQuotes, execCtx.Agent.Config)
	if err != nil {
		log.Error(ctx, "failed to select quote", zap.Error(err))
		return nil, err
	}

	log.Info(
		ctx,
		"selected quote",
		zap.String("source", string(selectedQuote.Source)),
		zap.String("type", string(selectedQuote.Type)),
		zap.Float64("targetRate", selectedQuote.Rates.Target),
	)

	// Apply margin/markup and follow guardrails
	finalRate, marginType, marginValue, err := applyQuoteMarginAndGuardrails(
		selectedQuote,
		execCtx.Agent.Config,
		execCtx.Service,
	)
	if err != nil {
		log.Error(ctx, "quote failed guardrails", zap.Error(err))
		return nil, err
	}

	// Prepare quick quote reply email template data
	templateData := prepareTemplateData(
		stops,
		transportType,
		selectedQuote,
		finalRate,
		suggestedRequest.Customer.Name,
	)

	// Store result in execution context for next tasks
	execCtx.PricingLookupTaskResult = &PricingLookupTaskResult{
		SelectedQuote: selectedQuote,
		FinalRate:     finalRate,
		Distance:      selectedQuote.Distance,
		TemplateData:  templateData,
	}

	log.Info(
		ctx,
		"Quick Quote Agent: Pricing Lookup Task Completed Successfully",
		zap.Float64("finalRate", finalRate),
		zap.String("source", string(selectedQuote.Source)),
	)

	marginPercent := marginValue
	if marginType == models.Amount {
		// For flat amount margins, MarginPercent should be 0
		marginPercent = 0
	}

	// Prepare task invocation metadata
	metadata := &agentmodels.TaskInvocationMetadata{
		PricingLookupTaskInvocation: &agentmodels.PricingLookupTaskInvocation{
			SelectedQuoteSource: selectedQuote.Source,
			TransportType:       transportType,
			OriginalRate:        selectedQuote.Rates.Target,
			FinalRate:           finalRate,
			MarginPercent:       marginPercent,
			MarginType:          marginType,
			MarginValue:         marginValue,
			Distance:            selectedQuote.Distance,
			NumQuotesReceived:   len(pricingResult.Quotes),
			NumQuoteErrors:      len(pricingResult.QuoteErrors),
		},
	}

	return metadata, nil
}

// handleDraftReplyEmail executes the draft email task.
// It uses the pricing result from the execution context to draft an email reply.
// Returns task metadata for invocation tracking.
func handleDraftReplyEmail(
	ctx context.Context,
	execCtx *TaskExecutionContext,
	task *agentmodels.QuickQuoteAgentTask,
) (*agentmodels.TaskInvocationMetadata, error) {

	ctx, span := otel.StartSpan(ctx, "agent.handleDraftReplyEmail", nil)
	var err error
	defer func() { span.End(err) }()

	log.Info(
		ctx,
		"executing draft email task",
		zap.Uint("taskID", task.ID),
		zap.Uint("agentID", execCtx.Agent.ID),
	)

	// Verify pricing result is available
	if execCtx.PricingLookupTaskResult == nil {
		err = errors.New("no pricing result available for email draft")
		log.Error(ctx, "missing pricing result", zap.Error(err))
		return nil, err
	}

	// Prepare draft input
	draftInput := DraftEmailReplyInput{
		Agent:        execCtx.Agent,
		Invocation:   execCtx.Invocation,
		User:         execCtx.User,
		Service:      execCtx.Service,
		Email:        execCtx.Email,
		QuoteRequest: execCtx.QuoteRequest,
		TemplateData: execCtx.PricingLookupTaskResult.TemplateData,
	}

	// Draft the email (without task invocation tracking since handler manages it)
	draftResult, err := draftEmailReplyWithoutTracking(ctx, draftInput)
	if err != nil {
		log.Error(ctx, "failed to draft email", zap.Error(err))
		return nil, err
	}

	// Update invocation with generated email ID
	execCtx.Invocation.GeneratedEmailID = &draftResult.GeneratedEmail.ID
	execCtx.Invocation.GeneratedEmail = draftResult.GeneratedEmail

	// Store draft email result in execution context
	execCtx.DraftEmailTaskResult = &DraftEmailTaskResult{
		GeneratedEmailID: draftResult.GeneratedEmail.ID,
		ExternalID:       draftResult.ExternalID,
	}

	log.Info(
		ctx,
		"draft email task completed successfully",
		zap.Uint("generatedEmailID", draftResult.GeneratedEmail.ID),
		zap.String("externalID", draftResult.ExternalID),
	)

	// Prepare task invocation metadata
	metadata := &agentmodels.TaskInvocationMetadata{
		DraftEmailTaskInvocation: &agentmodels.DraftEmailTaskInvocation{
			GeneratedEmailID: &draftResult.GeneratedEmail.ID,
		},
	}

	return metadata, nil
}

// draftEmailReplyWithoutTracking is a version of DraftEmailReply without task invocation tracking.
// This is used by the handler since the orchestrator manages task invocation tracking.
func draftEmailReplyWithoutTracking(
	ctx context.Context,
	input DraftEmailReplyInput,
) (*DraftEmailReplyResult, error) {
	// This is essentially the same as DraftEmailReply but without the
	// startTaskInvocation/endTaskInvocation calls.
	// We'll refactor DraftEmailReply to remove tracking in the cleanup step.
	// For now, call the existing function which will be cleaned up later.
	return DraftEmailReply(ctx, input)
}
