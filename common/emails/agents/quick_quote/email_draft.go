package quickquoteagent

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	genEmailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
	"github.com/drumkitai/drumkit/fn/api/env"
)

var (
	ErrUnsupportedEmailProvider = errors.New("unsupported email provider for agent drafting")
	ErrDraftCreationFailed      = errors.New("failed to create email draft")
)

type DraftEmailReplyInput struct {
	Agent        *agentmodels.QuickQuoteAgent
	Invocation   agentmodels.QuickQuoteAgentInvocation
	User         models.User
	Service      *models.Service
	Email        models.Email
	QuoteRequest models.QuoteRequest
	TemplateData models.QuickQuoteTemplateData
}

type DraftEmailReplyResult struct {
	GeneratedEmail *models.GeneratedEmail
	ExternalID     string // The email provider's ID for the draft
}

// DraftEmailReply creates a draft email reply in the user's email client.
// This creates a GeneratedEmail record and drafts the email without sending it.
func DraftEmailReply(ctx context.Context, input DraftEmailReplyInput) (_ *DraftEmailReplyResult, err error) {
	ctx, span := otel.StartSpan(ctx, "agent.DraftEmailReply", nil)
	defer func() { span.End(err) }()

	log.Info(
		ctx,
		"drafting email reply for quick quote agent",
		zap.Uint("invocationID", input.Invocation.ID),
		zap.String("emailProvider", string(input.User.EmailProvider)),
		zap.String("threadID", input.Email.ThreadID),
	)

	emailBody := buildQuickQuoteEmailBody(input.TemplateData)

	// Parse the original recipients from the email. DraftReplyAll() will reply to all recipients,
	// so we store all original recipients to maintain data consistency
	originalRecipients := parseEmailAddresses(input.Email.Recipients)

	// Create GeneratedEmail record
	generatedEmail := &models.GeneratedEmail{
		ServiceID:         input.Service.ID,
		Service:           *input.Service,
		UserID:            input.User.ID,
		User:              input.User,
		TriggeredByUserID: input.User.ID,
		ThreadID:          input.Email.ThreadID,
		Recipients:        originalRecipients, // Store all original recipients to match DraftReplyAll
		Subject:           "",                 // Replies don't need a subject
		SuggestedBody:     emailBody,
		AppliedBody:       emailBody,
		Status:            models.PendingStatus,
		ScheduleSend:      models.NullTime{Time: time.Now(), Valid: true},

		// Agent associations
		QuickQuoteAgentID:           &input.Agent.ID,
		QuickQuoteAgentInvocationID: &input.Invocation.ID,
	}

	if err = genEmailDB.BatchCreateGeneratedEmails(ctx, []*models.GeneratedEmail{generatedEmail}); err != nil {
		log.Error(ctx, "failed to create GeneratedEmail record", zap.Error(err))
		return nil, fmt.Errorf("failed to save generated email: %w", err)
	}

	log.Info(ctx, "created GeneratedEmail record", zap.Uint("generatedEmailID", generatedEmail.ID))

	// Draft the email based on the user's email provider
	var externalID string
	switch input.User.EmailProvider {
	case models.OutlookEmailProvider:
		externalID, err = draftOutlookReply(ctx, input, generatedEmail)
		if err != nil {
			generatedEmail.Status = models.FailedStatus
			if updateErr := genEmailDB.Update(ctx, generatedEmail); updateErr != nil {
				log.Warn(ctx, "failed to update GeneratedEmail status to failed", zap.Error(updateErr))
			}
			return nil, fmt.Errorf("%w: %w", ErrDraftCreationFailed, err)
		}

	case models.GmailEmailProvider:
		externalID, err = draftGmailReply(ctx, input, generatedEmail)
		if err != nil {
			generatedEmail.Status = models.FailedStatus
			if updateErr := genEmailDB.Update(ctx, generatedEmail); updateErr != nil {
				log.Warn(ctx, "failed to update GeneratedEmail status to failed", zap.Error(updateErr))
			}
			return nil, fmt.Errorf("%w: %w", ErrDraftCreationFailed, err)
		}

	case models.FrontEmailProvider:
		// Front draft support will be implemented in a follow-up
		log.Warn(ctx, "Front draft support not yet implemented, skipping email draft")
		externalID = "" // No external ID for Front yet

	default:
		err = fmt.Errorf("%w: %s", ErrUnsupportedEmailProvider, input.User.EmailProvider)
		log.Error(ctx, "unsupported email provider", zap.String("provider", string(input.User.EmailProvider)))
		return nil, err
	}

	// Update the GeneratedEmail with the external ID if we got one
	if externalID != "" {
		generatedEmail.ExternalID = externalID
		if err = genEmailDB.Update(ctx, generatedEmail); err != nil {
			log.Warn(ctx, "failed to update GeneratedEmail with external ID", zap.Error(err))
			// Non-fatal error - the draft was created, just not linked
			err = nil // Clear error so span doesn't report failure on success
		}
	}

	return &DraftEmailReplyResult{
		GeneratedEmail: generatedEmail,
		ExternalID:     externalID,
	}, nil
}

// draftOutlookReply creates a draft reply in Outlook.
func draftOutlookReply(
	ctx context.Context,
	input DraftEmailReplyInput,
	generatedEmail *models.GeneratedEmail,
) (string, error) {

	client, err := msclient.New(
		ctx,
		env.Vars.MicrosoftClientID,
		env.Vars.MicrosoftClientSecret,
		&input.User,
	)
	if err != nil {
		log.Error(ctx, "failed to create Outlook client", zap.Error(err))
		return "", fmt.Errorf("failed to create Outlook client: %w", err)
	}

	// Get the message we're replying to
	// We use the email's external ID (the Outlook message ID) to create the reply
	// ThreadID cannot be used as a fallback because it's a conversation identifier,
	// not a message ID. The Graph API endpoint /me/messages/{messageId}/createReplyAll
	// requires a specific message ID.
	messageID := input.Email.ExternalID
	if messageID == "" {
		return "", errors.New(
			"external ID (message ID) is required for creating a reply draft; thread ID cannot be used as a message ID",
		)
	}

	log.Info(
		ctx,
		"creating Outlook draft reply",
		zap.String("messageID", messageID),
		zap.String("threadID", input.Email.ThreadID),
	)

	msg, err := client.DraftReplyAll(ctx, messageID, generatedEmail.AppliedBody)
	if err != nil {
		log.Error(ctx, "failed to create Outlook draft reply", zap.Error(err))
		return "", fmt.Errorf("failed to create draft reply: %w", err)
	}

	log.Info(
		ctx,
		"created Outlook draft reply",
		zap.String("draftID", msg.ID),
		zap.String("conversationID", msg.ConversationID),
	)

	return msg.ID, nil
}

// draftGmailReply creates a draft reply in Gmail.
func draftGmailReply(
	ctx context.Context,
	input DraftEmailReplyInput,
	generatedEmail *models.GeneratedEmail,
) (string, error) {

	client, err := gmailclient.New(
		ctx,
		env.Vars.GoogleClientID,
		env.Vars.GoogleClientSecret,
		&input.User,
	)
	if err != nil {
		log.Error(ctx, "failed to create Gmail client", zap.Error(err))
		return "", fmt.Errorf("failed to create Gmail client: %w", err)
	}

	// Get the message we're replying to
	// We use the email's external ID (the Gmail message ID) to create the reply
	// ThreadID cannot be used as a fallback because the Gmail API requires a specific message ID
	// to create a reply draft with proper threading headers.
	messageID := input.Email.ExternalID
	if messageID == "" {
		return "", errors.New(
			"external ID (message ID) is required for creating a reply draft; thread ID cannot be used as a message ID",
		)
	}

	log.Info(
		ctx,
		"creating Gmail draft reply",
		zap.String("messageID", messageID),
		zap.String("threadID", input.Email.ThreadID),
	)

	draft, err := client.DraftReplyAll(ctx, messageID, generatedEmail.AppliedBody)
	if err != nil {
		log.Error(ctx, "failed to create Gmail draft reply", zap.Error(err))
		return "", fmt.Errorf("failed to create draft reply: %w", err)
	}

	log.Info(
		ctx,
		"created Gmail draft reply",
		zap.String("draftID", draft.Id),
		zap.String("messageID", draft.Message.Id),
		zap.String("threadID", draft.Message.ThreadId),
	)

	return draft.Id, nil
}

// buildQuickQuoteEmailBody constructs the email body for quick quote agent replies.
// This builds the body programmatically rather than using template syntax, making it
// compatible with both the agent (Go) and frontend (mustache.js) flows.
func buildQuickQuoteEmailBody(data models.QuickQuoteTemplateData) string {
	var body strings.Builder

	body.WriteString("<p>")

	if data.IsMultiStop && data.StopCount > 2 {
		body.WriteString(fmt.Sprintf(
			"Thank you for your %d-stop %s request from %s to %s.",
			data.StopCount,
			data.TransportType,
			data.PickupLocation,
			data.DeliveryLocation,
		))
	} else {
		body.WriteString(fmt.Sprintf(
			"Thank you for your %s request from %s to %s.",
			data.TransportType,
			data.PickupLocation,
			data.DeliveryLocation,
		))
	}

	body.WriteString(fmt.Sprintf(" The rate would be %s.", data.Rate))
	body.WriteString("</p>")

	return body.String()
}
