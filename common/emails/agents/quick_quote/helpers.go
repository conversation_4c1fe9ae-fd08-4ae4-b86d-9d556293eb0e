package quickquoteagent

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strings"

	"github.com/lib/pq"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	quickquoteagentDB "github.com/drumkitai/drumkit/common/rds/agents/quick_quote"
	quickquoteagenttaskDB "github.com/drumkitai/drumkit/common/rds/agents/quick_quote/task"
)

var (
	ErrNoQuotesFound   = errors.New("no quotes found from any pricing integration")
	ErrNoMatchingQuote = errors.New("no quote matches the selection criteria")
	ErrBelowMinQuote   = errors.New("calculated rate is below minimum guardrail")
	ErrAboveMaxQuote   = errors.New("calculated rate is above maximum guardrail")
	ErrInvalidRate     = errors.New("quote has invalid or zero rate")
)

// PricingLookupTaskResult holds the output of the pricing lookup task handler.
// This is stored in the execution context for use by subsequent tasks.
type PricingLookupTaskResult struct {
	SelectedQuote *AgentQuote
	FinalRate     float64
	Distance      float64
	TemplateData  models.QuickQuoteTemplateData
}

// DraftEmailTaskResult holds the output of the draft email task handler.
// This is stored in the execution context for tracking purposes.
type DraftEmailTaskResult struct {
	GeneratedEmailID uint
	ExternalID       string
}

// checkAndHandleAgentNoTasks checks if the agent has tasks configured and creates default tasks if not.
func checkAndHandleAgentNoTasks(ctx context.Context, execCtx *TaskExecutionContext) error {
	if len(execCtx.Agent.Config.Tasks) == 0 {
		log.Info(
			ctx,
			"no tasks found for existing agent, creating default tasks",
			zap.Uint("agentID", execCtx.Agent.ID),
		)

		if err := quickquoteagenttaskDB.CreateDefaultTasksForAgent(ctx, execCtx.Agent.ID); err != nil {
			return fmt.Errorf("failed to create default tasks: %w", err)
		}

		// Reload agent with tasks
		agent, err := quickquoteagentDB.GetAgentByID(ctx, execCtx.Agent.ID)
		if err != nil {
			return fmt.Errorf("failed to reload agent after creating tasks: %w", err)
		}
		if agent == nil {
			return fmt.Errorf("agent not found after creating tasks: agentID=%d", execCtx.Agent.ID)
		}
		execCtx.Agent = agent

		if len(execCtx.Agent.Config.Tasks) == 0 {
			return errors.New("failed to load tasks after creation")
		}
	}

	return nil
}

// buildStopsFromQuoteRequest extracts stops from the quote request's suggested fields.
func buildStopsFromQuoteRequest(suggested models.QuoteLoadInfo) []models.Stop {
	var stops []models.Stop

	// If multi-stop data is available, use it
	if len(suggested.Stops) > 0 {
		return suggested.Stops
	}

	pickupCityStatePopulated := suggested.PickupLocation.City != "" && suggested.PickupLocation.State != ""
	pickupZipPopulated := suggested.PickupLocation.Zip != ""
	deliveryCityStatePopulated := suggested.DeliveryLocation.City != "" && suggested.DeliveryLocation.State != ""
	deliveryZipPopulated := suggested.DeliveryLocation.Zip != ""

	// Fall back to legacy pickup/delivery location fields
	// Check both city and state (not just zip) since some quote requests may only have city/state
	if pickupCityStatePopulated || pickupZipPopulated {
		stops = append(stops, models.Stop{
			StopNumber: 0,
			Order:      0,
			StopType:   "pickup",
			Address:    suggested.PickupLocation,
		})
	}

	if deliveryCityStatePopulated || deliveryZipPopulated {
		stops = append(stops, models.Stop{
			StopNumber: 1,
			Order:      1,
			StopType:   "dropoff",
			Address:    suggested.DeliveryLocation,
		})
	}

	return stops
}

// selectQuoteByStrategy selects a quote based on the agent's configuration.
// Currently supports sourceOverride strategy; others can be added later.
func selectQuoteByStrategy(
	ctx context.Context,
	quotes []AgentQuote,
	agentConfig agentmodels.QuickQuoteAgentConfig,
) (*AgentQuote, error) {
	if len(quotes) == 0 {
		return nil, ErrNoQuotesFound
	}

	switch agentConfig.QuoteSelectionStrategy {
	case agentmodels.SourceOverrideStrategy:
		return selectQuoteBySourceOverride(quotes, agentConfig.QuoteSourceOverride)

	case agentmodels.LowestCostStrategy:
		return selectQuoteByLowestCost(quotes)

	case agentmodels.HighestCostStrategy:
		return selectQuoteByHighestCost(quotes)

	case agentmodels.MostConfidentStrategy:
		// For now, return first valid quote (positive rate)
		// TODO: Implement confidence-based selection when we have confidence data
		log.Warn(ctx, "most confident strategy not implemented, returning first valid quote", zap.Any("quotes", quotes))
		for i := range quotes {
			if quotes[i].Rates.Target > 0 {
				return &quotes[i], nil
			}
		}
		return nil, ErrNoQuotesFound

	default:
		log.Warn(
			ctx,
			"unexpected strategy, returning first valid quote",
			zap.String("strategy", string(agentConfig.QuoteSelectionStrategy)),
		)

		// Default to first valid quote if no strategy specified
		for i := range quotes {
			if quotes[i].Rates.Target > 0 {
				return &quotes[i], nil
			}
		}
		return nil, ErrNoQuotesFound
	}
}

// selectQuoteBySourceOverride selects a quote from a specific source.
// Returns an error if the quote has a zero or negative target rate.
func selectQuoteBySourceOverride(
	quotes []AgentQuote,
	sourceOverride models.QuoteSource,
) (*AgentQuote, error) {
	if sourceOverride == "" {
		// No override specified, return first valid quote
		for i := range quotes {
			if quotes[i].Rates.Target > 0 {
				return &quotes[i], nil
			}
		}
		return nil, ErrNoQuotesFound
	}

	for i := range quotes {
		if quotes[i].Source == sourceOverride {
			// Validate rate is positive
			if quotes[i].Rates.Target <= 0 {
				return nil,
					fmt.Errorf(
						"selected quote from %s has invalid rate %.2f",
						sourceOverride,
						quotes[i].Rates.Target,
					)
			}
			return &quotes[i], nil
		}
	}

	return nil, fmt.Errorf("%w: no quote from source %s", ErrNoMatchingQuote, sourceOverride)
}

// selectQuoteByLowestCost selects the quote with the lowest target rate.
// Skips quotes with zero or negative rates.
func selectQuoteByLowestCost(quotes []AgentQuote) (*AgentQuote, error) {
	if len(quotes) == 0 {
		return nil, ErrNoQuotesFound
	}

	var lowest *AgentQuote
	for i := range quotes {
		// Skip quotes with zero or negative rates
		if quotes[i].Rates.Target <= 0 {
			continue
		}

		if lowest == nil || quotes[i].Rates.Target < lowest.Rates.Target {
			lowest = &quotes[i]
		}
	}

	if lowest == nil {
		return nil, ErrNoQuotesFound
	}

	return lowest, nil
}

// selectQuoteByHighestCost selects the quote with the highest target rate.
// Skips quotes with zero or negative rates.
func selectQuoteByHighestCost(quotes []AgentQuote) (*AgentQuote, error) {
	if len(quotes) == 0 {
		return nil, ErrNoQuotesFound
	}

	var highest *AgentQuote
	for i := range quotes {
		// Skip quotes with zero or negative rates
		if quotes[i].Rates.Target <= 0 {
			continue
		}

		if highest == nil || quotes[i].Rates.Target > highest.Rates.Target {
			highest = &quotes[i]
		}
	}

	if highest == nil {
		return nil, ErrNoQuotesFound
	}

	return highest, nil
}

// applyQuoteMarginAndGuardrails applies the agent's margin configuration and validates
// against min/max guardrails. It accounts for service configuration to determine whether
// to use margin or markup formulas, and whether to use flat amount or percentage.
func applyQuoteMarginAndGuardrails(
	quote *AgentQuote,
	agentConfig agentmodels.QuickQuoteAgentConfig,
	service *models.Service,
) (float64, models.MarginType, float64, error) {
	baseRate := quote.Rates.Target

	marginType, marginValue := determineMarginTypeAndValue(agentConfig, service)

	// Apply margin/markup calculation
	finalRate := applyMarginCalculation(baseRate, marginType, marginValue, service)

	// Add fuel surcharge if configured
	finalRate = applyFuelSurcharge(finalRate, agentConfig, quote.Distance)

	finalRate, err := validateAgainstGuardrails(finalRate, agentConfig)

	// Round to 2 decimal places for dollar amounts
	finalRate = math.Round(finalRate*100) / 100

	return finalRate, marginType, marginValue, err
}

// determineMarginTypeAndValue extracts margin configuration from agent and service configs
func determineMarginTypeAndValue(
	agentConfig agentmodels.QuickQuoteAgentConfig,
	service *models.Service,
) (models.MarginType, float64) {

	marginType := models.Percentage
	var marginValue float64

	// If agent has MarginPercentOverride set, always treat it as a percentage, regardless of service's defaults
	if agentConfig.MarginPercentOverride != nil {
		marginType = models.Percentage
		marginValue = *agentConfig.MarginPercentOverride
		return marginType, marginValue
	}

	// Otherwise, use service's quick quote config if available
	if service != nil && service.QuickQuoteConfig != nil {
		marginType = service.QuickQuoteConfig.DefaultMarginType

		if marginType == models.Amount {
			marginValue = float64(service.QuickQuoteConfig.DefaultFlatMargin)
		} else {
			marginValue = float64(service.QuickQuoteConfig.DefaultPercentMargin)
		}
	}

	return marginType, marginValue
}

// applyMarginCalculation applies the appropriate margin/markup formula based on type
func applyMarginCalculation(
	baseRate float64,
	marginType models.MarginType,
	marginValue float64,
	service *models.Service,
) float64 {
	if marginType == models.Amount {
		// Flat margin: Add the margin amount to the carrier cost
		return baseRate + marginValue
	}

	// Percentage margin/markup
	// Check if service uses margin (vs markup) formula
	isMarginEnabled := service != nil && service.IsQuoteCalculatorMarginEnabled

	if isMarginEnabled {
		// Margin formula: Price = Cost / (1 - Profit_Percentage)
		// Profit margin cannot be 100% or more
		if marginValue >= 100 {
			// Using 99.9% as a practical upper limit to avoid division by zero or negative
			marginValue = 99.9
		}
		return baseRate / (1 - marginValue/100)
	}

	// Markup formula: Price = Cost * (1 + Markup_Percentage)
	return baseRate * (1 + marginValue/100)
}

// applyFuelSurcharge adds fuel surcharge to the rate if configured
func applyFuelSurcharge(rate float64, config agentmodels.QuickQuoteAgentConfig, distance float64) float64 {
	if config.FuelSurchargePerMileUSDOverride > 0 && distance > 0 {
		rate += config.FuelSurchargePerMileUSDOverride * distance
	}
	return rate
}

// validateAgainstGuardrails checks if the rate falls within configured min/max bounds
func validateAgainstGuardrails(rate float64, config agentmodels.QuickQuoteAgentConfig) (float64, error) {
	if config.MinQuoteValue.Val > 0 && rate < float64(config.MinQuoteValue.Val) {
		return 0, fmt.Errorf("%w: rate %.2f below minimum %.2f",
			ErrBelowMinQuote, rate, config.MinQuoteValue.Val)
	}

	if config.MaxQuoteValue.Val > 0 && rate > float64(config.MaxQuoteValue.Val) {
		return 0, fmt.Errorf("%w: rate %.2f above maximum %.2f",
			ErrAboveMaxQuote, rate, config.MaxQuoteValue.Val)
	}

	return rate, nil
}

// prepareTemplateData builds the template data for the email body.
func prepareTemplateData(
	stops []models.Stop,
	transportType models.TransportType,
	quote *AgentQuote,
	finalRate float64,
	customerName string,
) models.QuickQuoteTemplateData {
	// Build location strings
	var pickupLocation, deliveryLocation string
	if len(stops) > 0 {
		pickupLocation = formatStopLocation(stops[0])
		deliveryLocation = formatStopLocation(stops[len(stops)-1])
	}

	// Format rate
	rate := formatCurrency(finalRate)

	// Format rate per mile if distance is available
	var ratePerMile string
	if quote.Distance > 0 {
		ratePerMile = fmt.Sprintf("$%.2f/mi", finalRate/quote.Distance)
	}

	// Format distance
	var distance string
	if quote.Distance > 0 {
		distance = fmt.Sprintf("%.0f miles", quote.Distance)
	}

	return models.QuickQuoteTemplateData{
		PickupLocation:   pickupLocation,
		DeliveryLocation: deliveryLocation,
		TransportType:    formatTransportType(transportType),
		Distance:         distance,
		IsMultiStop:      len(stops) > 2,
		StopCount:        len(stops),
		Rate:             rate,
		RatePerMile:      ratePerMile,
		QuoteSource:      string(quote.Source),
		CustomerName:     customerName,
	}
}

// formatStopLocation formats a stop's location as "City, State".
func formatStopLocation(stop models.Stop) string {
	if stop.Address.City != "" && stop.Address.State != "" {
		return fmt.Sprintf("%s, %s", stop.Address.City, stop.Address.State)
	}
	if stop.Address.Zip != "" {
		return stop.Address.Zip
	}
	return ""
}

// formatCurrency formats a float as a currency string with comma separators for thousands.
func formatCurrency(amount float64) string {
	// Handle NaN and Inf values
	if math.IsNaN(amount) || math.IsInf(amount, 0) {
		return "$0.00"
	}

	// Format with 2 decimal places
	formatted := fmt.Sprintf("%.2f", amount)

	// Split into integer and decimal parts
	parts := strings.Split(formatted, ".")
	integerPart := parts[0]
	decimalPart := parts[1]

	// Add commas to integer part
	var result strings.Builder
	length := len(integerPart)
	for i, char := range integerPart {
		if i > 0 && (length-i)%3 == 0 {
			result.WriteString(",")
		}
		result.WriteRune(char)
	}

	return fmt.Sprintf("$%s.%s", result.String(), decimalPart)
}

// formatTransportType formats the transport type for display.
func formatTransportType(t models.TransportType) string {
	return strings.ToLower(string(t))
}

// parseEmailAddresses converts a comma-delimited string of email addresses into a string array.
// Empty strings and whitespace-only addresses are filtered out.
func parseEmailAddresses(emails string) pq.StringArray {
	if emails == "" {
		return pq.StringArray{}
	}

	addresses := strings.Split(emails, ",")
	var parsedAddresses pq.StringArray

	for _, addr := range addresses {
		trimmed := strings.TrimSpace(addr)
		if trimmed != "" {
			parsedAddresses = append(parsedAddresses, trimmed)
		}
	}

	return parsedAddresses
}
