package quickquoteagent

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
)

func TestSelectQuoteByStrategy(t *testing.T) {
	ctx := context.Background()
	// Sample quotes for testing
	datQuote := AgentQuote{
		Source: models.DATSource,
		Type:   models.DATSpotType,
		Rates: AgentQuoteRates{
			Target: 1000,
			Low:    900,
			High:   1100,
		},
		Distance: 500,
	}

	gsQuote := AgentQuote{
		Source: models.GreenscreensSource,
		Type:   models.GSBuyPowerType,
		Rates: AgentQuoteRates{
			Target: 1200,
			Low:    1100,
			High:   1300,
		},
		Distance: 500,
	}

	truckstopQuote := AgentQuote{
		Source: models.TruckStopSource,
		Type:   models.TruckStopBookedType,
		Rates: AgentQuoteRates{
			Target: 800,
			Low:    700,
			High:   900,
		},
		Distance: 500,
	}

	zeroRateQuote := AgentQuote{
		Source: models.TruckStopSource,
		Type:   models.TruckStopBookedType,
		Rates: AgentQuoteRates{
			Target: 0, // Invalid zero rate
			Low:    700,
			High:   900,
		},
		Distance: 500,
	}

	quotes := []AgentQuote{datQuote, gsQuote, truckstopQuote}

	t.Run("sourceOverride selects DAT", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			QuoteSelectionStrategy: agentmodels.SourceOverrideStrategy,
			QuoteSourceOverride:    models.DATSource,
		}

		selected, err := selectQuoteByStrategy(ctx, quotes, config)
		require.NoError(t, err)
		assert.Equal(t, models.DATSource, selected.Source)
		assert.Equal(t, float64(1000), selected.Rates.Target)
	})

	t.Run("sourceOverride selects Greenscreens", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			QuoteSelectionStrategy: agentmodels.SourceOverrideStrategy,
			QuoteSourceOverride:    models.GreenscreensSource,
		}

		selected, err := selectQuoteByStrategy(ctx, quotes, config)
		require.NoError(t, err)
		assert.Equal(t, models.GreenscreensSource, selected.Source)
		assert.Equal(t, float64(1200), selected.Rates.Target)
	})

	t.Run("sourceOverride returns error for missing source", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			QuoteSelectionStrategy: agentmodels.SourceOverrideStrategy,
			QuoteSourceOverride:    "nonexistent",
		}

		_, err := selectQuoteByStrategy(ctx, quotes, config)
		require.Error(t, err)
		assert.ErrorIs(t, err, ErrNoMatchingQuote)
	})

	t.Run("sourceOverride rejects zero rate quote", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			QuoteSelectionStrategy: agentmodels.SourceOverrideStrategy,
			QuoteSourceOverride:    models.TruckStopSource,
		}

		zeroRateQuotes := []AgentQuote{zeroRateQuote}
		_, err := selectQuoteByStrategy(ctx, zeroRateQuotes, config)
		require.Error(t, err)
	})

	t.Run("lowestCost selects cheapest valid quote", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			QuoteSelectionStrategy: agentmodels.LowestCostStrategy,
		}

		selected, err := selectQuoteByStrategy(ctx, quotes, config)
		require.NoError(t, err)
		assert.Equal(t, models.TruckStopSource, selected.Source)
		assert.Equal(t, float64(800), selected.Rates.Target)
	})

	t.Run("lowestCost skips zero rate quotes", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			QuoteSelectionStrategy: agentmodels.LowestCostStrategy,
		}

		mixedQuotes := []AgentQuote{zeroRateQuote, datQuote, gsQuote}
		selected, err := selectQuoteByStrategy(ctx, mixedQuotes, config)
		require.NoError(t, err)
		assert.Equal(t, models.DATSource, selected.Source)
		assert.Equal(t, float64(1000), selected.Rates.Target)
	})

	t.Run("lowestCost returns error when all quotes have zero rate", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			QuoteSelectionStrategy: agentmodels.LowestCostStrategy,
		}

		zeroRateQuotes := []AgentQuote{zeroRateQuote}
		_, err := selectQuoteByStrategy(ctx, zeroRateQuotes, config)
		require.Error(t, err)
		assert.ErrorIs(t, err, ErrNoQuotesFound)
	})

	t.Run("highestCost selects most expensive quote", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			QuoteSelectionStrategy: agentmodels.HighestCostStrategy,
		}

		selected, err := selectQuoteByStrategy(ctx, quotes, config)
		require.NoError(t, err)
		assert.Equal(t, models.GreenscreensSource, selected.Source)
		assert.Equal(t, float64(1200), selected.Rates.Target)
	})

	t.Run("highestCost skips zero rate quotes", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			QuoteSelectionStrategy: agentmodels.HighestCostStrategy,
		}

		mixedQuotes := []AgentQuote{zeroRateQuote, datQuote, gsQuote}
		selected, err := selectQuoteByStrategy(ctx, mixedQuotes, config)
		require.NoError(t, err)
		assert.Equal(t, models.GreenscreensSource, selected.Source)
		assert.Equal(t, float64(1200), selected.Rates.Target)
	})

	t.Run("default strategy returns first valid quote", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{}

		selected, err := selectQuoteByStrategy(ctx, quotes, config)
		require.NoError(t, err)
		assert.Equal(t, models.DATSource, selected.Source)
	})

	t.Run("default strategy skips zero rate quotes", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{}

		mixedQuotes := []AgentQuote{zeroRateQuote, datQuote}
		selected, err := selectQuoteByStrategy(ctx, mixedQuotes, config)
		require.NoError(t, err)
		assert.Equal(t, models.DATSource, selected.Source)
	})

	t.Run("empty quotes returns error", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{}

		_, err := selectQuoteByStrategy(ctx, []AgentQuote{}, config)
		require.Error(t, err)
		assert.ErrorIs(t, err, ErrNoQuotesFound)
	})
}

func TestApplyQuoteMarginAndGuardrails(t *testing.T) {
	baseQuote := &AgentQuote{
		Source: models.DATSource,
		Rates: AgentQuoteRates{
			Target: 1000,
		},
		Distance: 500,
	}

	t.Run("no margin returns base rate", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{}

		finalRate, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, nil)
		require.NoError(t, err)
		assert.Equal(t, float64(1000), finalRate)
	})

	t.Run("applies markup percentage (default behavior)", func(t *testing.T) {
		margin := 15.0
		config := agentmodels.QuickQuoteAgentConfig{
			MarginPercentOverride: &margin,
		}

		// Service with markup enabled (default)
		service := &models.Service{
			FeatureFlags: models.FeatureFlags{
				IsQuoteCalculatorMarginEnabled: false,
			},
		}

		finalRate, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, service)
		require.NoError(t, err)
		assert.Equal(t, float64(1150), finalRate) // 1000 * 1.15 (markup)
	})

	t.Run("applies margin percentage when margin enabled", func(t *testing.T) {
		margin := 15.0
		config := agentmodels.QuickQuoteAgentConfig{
			MarginPercentOverride: &margin,
		}

		// Service with margin enabled
		service := &models.Service{
			FeatureFlags: models.FeatureFlags{
				IsQuoteCalculatorMarginEnabled: true,
			},
		}

		finalRate, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, service)
		require.NoError(t, err)
		// Margin formula: 1000 / (1 - 0.15) = 1000 / 0.85 ≈ 1176.47
		assert.InDelta(t, 1176.47, finalRate, 0.01)
	})

	t.Run("applies flat amount margin", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{}

		service := &models.Service{
			FeatureFlags: models.FeatureFlags{
				IsQuoteCalculatorMarginEnabled: false,
			},
			QuickQuoteConfig: &models.QuickQuoteConfig{
				DefaultMarginType: models.Amount,
				DefaultFlatMargin: 200,
			},
		}

		finalRate, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, service)
		require.NoError(t, err)
		assert.Equal(t, float64(1200), finalRate) // 1000 + 200
	})

	t.Run("applies fuel surcharge override", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			FuelSurchargePerMileUSDOverride: 0.50,
		}

		finalRate, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, nil)
		require.NoError(t, err)
		assert.Equal(t, float64(1250), finalRate) // 1000 + (0.50 * 500)
	})

	t.Run("applies both margin and fuel surcharge", func(t *testing.T) {
		margin := 10.0
		config := agentmodels.QuickQuoteAgentConfig{
			MarginPercentOverride:           &margin,
			FuelSurchargePerMileUSDOverride: 0.50,
		}

		// Service with markup enabled (default)
		service := &models.Service{
			FeatureFlags: models.FeatureFlags{
				IsQuoteCalculatorMarginEnabled: false,
			},
		}

		finalRate, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, service)
		require.NoError(t, err)
		// 1000 * 1.10 = 1100, then + (0.50 * 500) = 1350
		assert.Equal(t, float64(1350), finalRate)
	})

	t.Run("uses service default margin when agent override not set", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{}

		service := &models.Service{
			FeatureFlags: models.FeatureFlags{
				IsQuoteCalculatorMarginEnabled: false,
			},
			QuickQuoteConfig: &models.QuickQuoteConfig{
				DefaultMarginType:    models.Percentage,
				DefaultPercentMargin: 20,
			},
		}

		finalRate, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, service)
		require.NoError(t, err)
		assert.Equal(t, float64(1200), finalRate) // 1000 * 1.20 (markup)
	})

	t.Run("agent override takes precedence over service default", func(t *testing.T) {
		margin := 25.0
		config := agentmodels.QuickQuoteAgentConfig{
			MarginPercentOverride: &margin,
		}

		service := &models.Service{
			FeatureFlags: models.FeatureFlags{
				IsQuoteCalculatorMarginEnabled: false,
			},
			QuickQuoteConfig: &models.QuickQuoteConfig{
				DefaultMarginType:    models.Percentage,
				DefaultPercentMargin: 10,
			},
		}

		finalRate, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, service)
		require.NoError(t, err)
		assert.Equal(t, float64(1250), finalRate) // 1000 * 1.25 (uses agent override, not service default)
	})

	t.Run("MarginPercentOverride treated as percentage even when service default is Amount", func(t *testing.T) {
		margin := 15.0
		config := agentmodels.QuickQuoteAgentConfig{
			MarginPercentOverride: &margin,
		}

		// Service has DefaultMarginType = Amount, but agent override should be treated as percentage
		service := &models.Service{
			FeatureFlags: models.FeatureFlags{
				IsQuoteCalculatorMarginEnabled: false,
			},
			QuickQuoteConfig: &models.QuickQuoteConfig{
				DefaultMarginType: models.Amount,
				DefaultFlatMargin: 200, // This should be ignored when agent override is set
			},
		}

		finalRate, marginType, marginValue, err := applyQuoteMarginAndGuardrails(baseQuote, config, service)
		require.NoError(t, err)
		// Should apply 15% markup, NOT add $15
		assert.Equal(t, float64(1150), finalRate) // 1000 * 1.15
		assert.Equal(t, models.Percentage, marginType)
		assert.Equal(t, 15.0, marginValue)
	})

	t.Run("fails minimum guardrail", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			MinQuoteValue: models.ValueUnit{Val: 2000},
		}

		_, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, nil)
		require.Error(t, err)
		assert.ErrorIs(t, err, ErrBelowMinQuote)
	})

	t.Run("fails maximum guardrail", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			MaxQuoteValue: models.ValueUnit{Val: 500},
		}

		_, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, nil)
		require.Error(t, err)
		assert.ErrorIs(t, err, ErrAboveMaxQuote)
	})

	t.Run("passes guardrails when within range", func(t *testing.T) {
		config := agentmodels.QuickQuoteAgentConfig{
			MinQuoteValue: models.ValueUnit{Val: 500},
			MaxQuoteValue: models.ValueUnit{Val: 1500},
		}

		finalRate, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, nil)
		require.NoError(t, err)
		assert.Equal(t, float64(1000), finalRate)
	})

	t.Run("handles decimal margin percentage", func(t *testing.T) {
		margin := 15.5
		config := agentmodels.QuickQuoteAgentConfig{
			MarginPercentOverride: &margin,
		}

		service := &models.Service{
			FeatureFlags: models.FeatureFlags{
				IsQuoteCalculatorMarginEnabled: false,
			},
		}

		finalRate, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, service)
		require.NoError(t, err)
		assert.Equal(t, float64(1155), finalRate) // 1000 * 1.155 (markup)
	})

	t.Run("handles margin percentage >= 100%", func(t *testing.T) {
		margin := 100.0
		config := agentmodels.QuickQuoteAgentConfig{
			MarginPercentOverride: &margin,
		}

		service := &models.Service{
			FeatureFlags: models.FeatureFlags{
				IsQuoteCalculatorMarginEnabled: true,
			},
		}

		finalRate, _, _, err := applyQuoteMarginAndGuardrails(baseQuote, config, service)
		require.NoError(t, err)
		// Should cap at 99.9%: 1000 / (1 - 0.999) = 1000 / 0.001 = 1000000
		assert.Greater(t, finalRate, float64(100000))
	})
}

func TestBuildStopsFromQuoteRequest(t *testing.T) {
	t.Run("uses multi-stop data when available", func(t *testing.T) {
		suggested := models.QuoteLoadInfo{
			Stops: []models.Stop{
				{StopNumber: 0, Address: models.Address{City: "Chicago", State: "IL"}},
				{StopNumber: 1, Address: models.Address{City: "St. Louis", State: "MO"}},
				{StopNumber: 2, Address: models.Address{City: "Dallas", State: "TX"}},
			},
		}

		stops := buildStopsFromQuoteRequest(suggested)
		assert.Len(t, stops, 3)
		assert.Equal(t, "Chicago", stops[0].Address.City)
		assert.Equal(t, "Dallas", stops[2].Address.City)
	})

	t.Run("falls back to legacy pickup/delivery locations", func(t *testing.T) {
		suggested := models.QuoteLoadInfo{
			PickupLocation:   models.Address{City: "Chicago", State: "IL"},
			DeliveryLocation: models.Address{City: "Dallas", State: "TX"},
		}

		stops := buildStopsFromQuoteRequest(suggested)
		assert.Len(t, stops, 2)
		assert.Equal(t, "Chicago", stops[0].Address.City)
		assert.Equal(t, "Dallas", stops[1].Address.City)
	})

	t.Run("returns empty for no location data", func(t *testing.T) {
		suggested := models.QuoteLoadInfo{}

		stops := buildStopsFromQuoteRequest(suggested)
		assert.Len(t, stops, 0)
	})
}

func TestPrepareTemplateData(t *testing.T) {
	stops := []models.Stop{
		{StopNumber: 0, Address: models.Address{City: "Chicago", State: "IL"}},
		{StopNumber: 1, Address: models.Address{City: "Dallas", State: "TX"}},
	}

	quote := &AgentQuote{
		Source:   models.DATSource,
		Distance: 920,
		Rates: AgentQuoteRates{
			Target: 2000,
		},
	}

	t.Run("builds template data for two-stop route", func(t *testing.T) {
		data := prepareTemplateData(
			stops,
			models.VanTransportType,
			quote,
			2300, // final rate with margin
			"Test Customer",
		)

		assert.Equal(t, "Chicago, IL", data.PickupLocation)
		assert.Equal(t, "Dallas, TX", data.DeliveryLocation)
		assert.Equal(t, "van", data.TransportType)
		assert.Equal(t, "$2,300.00", data.Rate)
		assert.Equal(t, "920 miles", data.Distance)
		assert.False(t, data.IsMultiStop)
		assert.Equal(t, 2, data.StopCount)
		assert.Equal(t, "Test Customer", data.CustomerName)
		assert.Equal(t, "dat", data.QuoteSource)
	})

	t.Run("builds template data for multi-stop route", func(t *testing.T) {
		multiStops := []models.Stop{
			{StopNumber: 0, Address: models.Address{City: "Chicago", State: "IL"}},
			{StopNumber: 1, Address: models.Address{City: "St. Louis", State: "MO"}},
			{StopNumber: 2, Address: models.Address{City: "Dallas", State: "TX"}},
		}

		data := prepareTemplateData(
			multiStops,
			models.FlatbedTransportType,
			quote,
			2500,
			"",
		)

		assert.Equal(t, "Chicago, IL", data.PickupLocation)
		assert.Equal(t, "Dallas, TX", data.DeliveryLocation)
		assert.Equal(t, "flatbed", data.TransportType)
		assert.True(t, data.IsMultiStop)
		assert.Equal(t, 3, data.StopCount)
	})

	t.Run("calculates rate per mile", func(t *testing.T) {
		data := prepareTemplateData(
			stops,
			models.VanTransportType,
			quote,
			920, // exactly $1/mile
			"",
		)

		assert.Equal(t, "$1.00/mi", data.RatePerMile)
	})
}

func TestFormatStopLocation(t *testing.T) {
	t.Run("formats city and state", func(t *testing.T) {
		stop := models.Stop{
			Address: models.Address{City: "Chicago", State: "IL"},
		}
		assert.Equal(t, "Chicago, IL", formatStopLocation(stop))
	})

	t.Run("formats zip when no city/state", func(t *testing.T) {
		stop := models.Stop{
			Address: models.Address{Zip: "60601"},
		}
		assert.Equal(t, "60601", formatStopLocation(stop))
	})

	t.Run("returns empty for no location data", func(t *testing.T) {
		stop := models.Stop{}
		assert.Equal(t, "", formatStopLocation(stop))
	})
}

func TestFormatCurrency(t *testing.T) {
	assert.Equal(t, "$1,000.00", formatCurrency(1000))
	assert.Equal(t, "$1,234.56", formatCurrency(1234.56))
	assert.Equal(t, "$0.00", formatCurrency(0))
	assert.Equal(t, "$10,000.00", formatCurrency(10000))
	assert.Equal(t, "$123,456.78", formatCurrency(123456.78))
}

func TestFormatTransportType(t *testing.T) {
	assert.Equal(t, "van", formatTransportType(models.VanTransportType))
	assert.Equal(t, "reefer", formatTransportType(models.ReeferTransportType))
	assert.Equal(t, "flatbed", formatTransportType(models.FlatbedTransportType))
	assert.Equal(t, "hotshot", formatTransportType("HOTSHOT"))
}
