package tmsrefresh

import (
	"context"
	"errors"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/redis"
)

// GetRefreshState returns the cursor and updatedAt filter based on the provided options.
// It follows the hierarchy: explicit option > Redis state > DB column (if UseLastUpdatedAt) > default.
//
// The returned values are intended to be used by TMS implementations to set their specific
// request parameters (e.g., changedAfterDate, recordOffset, updated[gte], start).
func GetRefreshState(
	ctx context.Context,
	tmsID uint,
	jobType string,
	options *models.TMSOptions,
	updatedAtColumn integrationDB.Column,
	timeFormat string,
) (cursor string, updatedAt string) {
	// Priority for cursor: explicit option > Redis state
	if options.Cursor != "" {
		cursor = options.Cursor
	}

	// Priority for updatedAt: explicit option > Redis state > DB column (if UseLastUpdatedAt)
	if options.UpdatedAtFilter != "" {
		updatedAt = options.UpdatedAtFilter
	}

	// If either value is missing, try to fetch from Redis
	if cursor == "" || updatedAt == "" {
		savedUpdatedAt, savedCursor, err := redis.GetIntegrationState(ctx, tmsID, jobType)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to get integration state from Redis",
				zap.Error(err),
				zap.Uint("tms_id", tmsID),
				zap.String("job_type", jobType),
			)
		}

		if cursor == "" {
			cursor = savedCursor
		}
		if updatedAt == "" {
			updatedAt = savedUpdatedAt
		}
	}

	// If updatedAt is still missing and UseLastUpdatedAt is true, fetch from DB
	if updatedAt == "" && options.UseLastUpdatedAt {
		latestUpdatedAt, err := integrationDB.GetColumn(ctx, tmsID, updatedAtColumn)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Info(
					ctx,
					"record not found, fetching all data for integration",
					zap.Uint("integration_id", tmsID),
					zap.String("column", string(updatedAtColumn)),
				)
			} else {
				log.WarnNoSentry(
					ctx,
					"failed to get integration state from DB, fetching all data for integration",
					zap.Error(err),
					zap.Uint("integration_id", tmsID),
					zap.String("column", string(updatedAtColumn)),
				)
			}
		} else {
			updatedAt = latestUpdatedAt.Format(timeFormat)
		}
	}

	return cursor, updatedAt
}

// SetIntegrationStateWithWarning saves the current state of an integration job to Redis with an 'in_progress' status.
// This is typically called during pagination to ensure the job can be resumed if it times out or fails.
func SetIntegrationStateWithWarning(
	ctx context.Context,
	tmsID uint,
	jobType string,
	updatedAt string,
	cursor string,
) {
	if err := redis.SetIntegrationStateWithStatus(
		ctx,
		tmsID,
		jobType,
		redis.JobStatusInProgress,
		updatedAt,
		cursor,
	); err != nil {
		log.Warn(
			ctx,
			"failed to set integration state in Redis",
			zap.Error(err),
			zap.Uint("tms_id", tmsID),
			zap.String("job_type", jobType),
		)
	}
}
