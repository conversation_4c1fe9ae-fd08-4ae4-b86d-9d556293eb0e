package agentmodels

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
)

type QuickQuoteAgentInvocation struct {
	gorm.Model

	StartedAt       time.Time `json:"startedAt"`
	CompletedAt     time.Time `json:"completedAt"`
	DurationSeconds int       `json:"durationSeconds"`

	// Email & thread that triggered the invocation
	EmailID  uint         `gorm:"index" validate:"required"`
	Email    models.Email `json:"-"`
	ThreadID string       `gorm:"index" validate:"required"`

	ServiceID        uint           `gorm:"index" validate:"required"`
	Service          models.Service `json:"-"`
	UserID           uint           `gorm:"index" validate:"required"`
	User             models.User    `json:"-"`
	UserEmailAddress string         `json:"userEmailAddress"`

	// As of now QQ agents always draft/send emails, this may be optional in the future
	GeneratedEmailID *uint                  `gorm:"index" validate:"required"`
	GeneratedEmail   *models.GeneratedEmail `json:"-"`

	QuickQuoteAgentID uint            `gorm:"index" validate:"required"`
	QuickQuoteAgent   QuickQuoteAgent `json:"-"`

	QuoteRequestID uint                `gorm:"index" validate:"required"`
	QuoteRequest   models.QuoteRequest `json:"-"` // Future consideration: batch quotes

	Status             AgentInvocationStatus   `json:"status"`
	UserResponseStatus AgentUserResponseStatus `json:"userResponseStatus"`
	UserFeedback       string                  `json:"userFeedback"` // Maybe do structured feedback, TBD

	// Performance & Observability
	Error             *string `json:"error,omitempty"`
	BraintrustTraceID string  `json:"braintrustTraceId"`
	LambdaRequestID   string  `json:"lambdaRequestId"`

	//nolint:lll
	TaskInvocations []QuickQuoteAgentTaskInvocation `gorm:"foreignKey:QuickQuoteAgentInvocationID" json:"taskInvocations"`
}

// Data on the actual execution of a task by the agent
type QuickQuoteAgentTaskInvocation struct {
	gorm.Model

	ServiceID        uint           `gorm:"index" validate:"required"`
	Service          models.Service `json:"-"`
	UserID           uint           `gorm:"index" validate:"required"`
	User             models.User    `json:"-"`
	UserEmailAddress string         `json:"userEmailAddress"`

	QuickQuoteAgentInvocationID uint                      `gorm:"index" json:"-"`
	QuickQuoteAgentInvocation   QuickQuoteAgentInvocation `gorm:"foreignKey:QuickQuoteAgentInvocationID" json:"-"`

	QuickQuoteAgentID uint                  `gorm:"index" json:"quickQuoteAgentId"`
	QuickQuoteAgent   QuickQuoteAgent       `gorm:"foreignKey:QuickQuoteAgentID" json:"-"`
	AgentTaskID       uint                  `gorm:"index" json:"agentTaskId"`
	AgentTask         QuickQuoteAgentTask   `gorm:"foreignKey:AgentTaskID;references:ID" json:"-"`
	Step              int                   `json:"step"`   // e.g. 0, 1, 2, etc. same as AgentTask.Step
	Action            QuickQuoteAgentAction `json:"action"` // Same as AgentTask.Action

	StartedAt        time.Time             `json:"startedAt"`
	CompletedAt      time.Time             `json:"completedAt"`
	DurationSeconds  int                   `json:"durationSeconds"`
	Status           AgentInvocationStatus `json:"status"`
	Retries          int                   `json:"retries"`
	Error            *string               `json:"error,omitempty"`
	BraintrustSpanID string                `json:"braintrustSpanId"`

	// Task-specific metadata; I'm not set on this since we'll have Agent FKs in other tables already
	// (email, quote request)
	Metadata TaskInvocationMetadata `gorm:"type:JSONB" json:"metadata"`
}

type TaskInvocationMetadata struct {
	// Additional metadata for each task based on Action type
	*PricingLookupTaskInvocation `gorm:"type:JSONB" json:"pricingLookupTaskInvocation,omitempty"`
	*DraftEmailTaskInvocation    `gorm:"type:JSONB" json:"draftEmailTaskInvocation,omitempty"`

	// Future: additional metadata for each task based on Action type
	// *NotifyUserTaskInvocation `gorm:"type:JSONB" json:"notifyUserTaskInvocation,omitempty"`
}

type PricingLookupTaskInvocation struct {
	SelectedQuoteSource models.QuoteSource   `json:"selectedQuoteSource"`
	TransportType       models.TransportType `json:"transportType"`
	OriginalRate        float64              `json:"originalRate"`
	FinalRate           float64              `json:"finalRate"`
	MarginPercent       float64              `json:"marginPercent"`
	MarginType          models.MarginType    `json:"marginType"`
	MarginValue         float64              `json:"marginValue"`

	Distance          float64 `json:"distance"`
	NumQuotesReceived int     `json:"numQuotesReceived"`
	NumQuoteErrors    int     `json:"numQuoteErrors"`
}

type DraftEmailTaskInvocation struct {
	GeneratedEmailID *uint `json:"generatedEmailId"`
}

// Implement Scan and Value for TaskInvocationMetadata
func (t *TaskInvocationMetadata) Scan(value any) error {
	if value == nil {
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for TaskInvocationMetadata: %T", value)
	}
	var taskInvocationMetadata TaskInvocationMetadata
	if err := json.Unmarshal(val, &taskInvocationMetadata); err != nil {
		return err
	}

	*t = taskInvocationMetadata
	return nil
}

func (t TaskInvocationMetadata) Value() (driver.Value, error) {
	if t == (TaskInvocationMetadata{}) {
		return nil, nil
	}

	return json.Marshal(t)
}

var (
	_ sql.Scanner   = &TaskInvocationMetadata{}
	_ driver.Valuer = &TaskInvocationMetadata{}
)

// --------------------------------------------------------------
// 						 	ENUMS
// --------------------------------------------------------------

type AgentInvocationStatus string

const (
	InProgressInvocation AgentInvocationStatus = "inProgress"
	CompletedInvocation  AgentInvocationStatus = "completed"
	FailedInvocation     AgentInvocationStatus = "failed"
)

type AgentUserResponseStatus string

const (
	PendingUserResponse           AgentUserResponseStatus = "pending"
	AcceptedNoEditsUserResponse   AgentUserResponseStatus = "acceptedNoEdits"
	AcceptedWithEditsUserResponse AgentUserResponseStatus = "acceptedWithEdits"
	RejectedUserResponse          AgentUserResponseStatus = "rejected"
	// For agents that don't require user acceptance for agent to terminate flow,
	// e.g. agent can send email on its own without waiting for user to review and accept/reject
	// instead of agent drafting email and waiting for user to review and accept/reject
	NotApplicableUserResponse AgentUserResponseStatus = "notApplicable"
)
