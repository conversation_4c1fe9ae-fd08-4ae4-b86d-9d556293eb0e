package agentmodels

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"github.com/lib/pq"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
)

type QuickQuoteAgent struct {
	gorm.Model

	ServiceID uint           `gorm:"index"`
	Service   models.Service `json:"-"`

	IsServiceWide bool `gorm:"default:false;" json:"isServiceWide"`
	// Same agent can be used for multiple users and user groups; if a user is accidentally duplicated across groups,
	// agent should still only be invoked once for that user per event trigger
	Users      []models.User      `gorm:"many2many:quick_quote_agent_users;"`
	UserGroups []models.UserGroup `gorm:"many2many:quick_quote_agent_groups;"`

	Config QuickQuoteAgentConfig `gorm:"embedded;" json:"config"`

	Name  string `json:"name"`
	Notes string `json:"notes"`
	// As of: 12/18/25 we don't display agents on FE but in case we do on dashboards, client facing notes may be nice.
	InternalNotes string `json:"internalNotes"` // For internal drumkit notes that will never be shown to users.

	CreatedByType   AgentCreatedByType `json:"createdByType"`
	CreatedByUserID *uint              `json:"createdByUserID"` // Only set if CreatedBy is AgentCreatedByUser
}

type QuickQuoteAgentConfig struct {
	// Agent trigger filters
	//nolint:lll
	EmailSenderIncludeList pq.StringArray `gorm:"type:text[]" json:"emailIncludeList"` // e.g. "<EMAIL> OR customer.com"
	//nolint:lll
	EmailSenderExcludeList pq.StringArray `gorm:"type:text[]" json:"emailExcludeList"` // e.g. "<EMAIL> OR customer.com"

	// Future
	// PickupLocationExcludeList pq.StringArray `gorm:"type:text[]" json:"pickupLocationExcludeList"`
	// DropoffLocationExcludeList pq.StringArray `gorm:"type:text[]" json:"dropoffLocationExcludeList"`

	// Lane data overrides
	TransportTypeOverride models.TransportType `json:"transportTypeOverride"` // "flatbed" for this PLS use case

	// Quote generation config
	QuoteSelectionStrategy          QuoteSelectionStrategy `json:"quoteSelectionStrategy"`
	MarginPercentOverride           *float64               `json:"marginPercentOverride"` // e.g. 15% for PLS
	FuelSurchargePerMileUSDOverride float64                `json:"fuelSurchargePerMileUSDOverride"`
	// Regardless of if multiple quote sources are available,
	// always use select this source's quote (e.g. Greenscreens, DAT), etc
	// Valid only if QuoteSelectionStrategy is SourceOverrideStrategy
	QuoteSourceOverride models.QuoteSource `json:"quoteSourceOverride"`

	// Future
	// MarginStrategy MarginStrategy `json:"marginStrategy"` e.g. historical data, LLM-generated, etc.

	// Guardrails
	MinQuoteValue     models.ValueUnit `gorm:"type:JSONB" json:"minQuoteValue"` // e.g. $100 USD
	MaxQuoteValue     models.ValueUnit `gorm:"type:JSONB" json:"maxQuoteValue"` // e.g. $10,000 USD
	MaxRetriesAllowed int              `gorm:"default:2" json:"maxRetriesAllowed"`

	// What to do after agent completes its work, e.g. DraftEmailAction, SendEmailAction, etc.
	// In the future may need to support multiple actions (e.g. draft email and submit quote to TMS)
	Tasks []QuickQuoteAgentTask `gorm:"foreignKey:QuickQuoteAgentID" json:"tasks"`
}

// Definition of a task that can be performed by the agent
type QuickQuoteAgentTask struct {
	gorm.Model

	QuickQuoteAgentID uint            `gorm:"index:idx_agent_task_step,priority:1" json:"-"`
	QuickQuoteAgent   QuickQuoteAgent `gorm:"foreignKey:QuickQuoteAgentID" json:"-"`

	Step              int                   `gorm:"index:idx_agent_task_step,priority:2" json:"step"` // e.g. 0, 1, 2
	Action            QuickQuoteAgentAction `json:"action"`
	MaxRetriesAllowed int                   `gorm:"default:1" json:"maxRetriesAllowed"`
	// Whether to fail-open if the task fails; useful for tasks that are not critical to the agent's success
	FailOpen bool `gorm:"default:false" json:"failOpen"`

	// Task metadata by action type
	Metadata TaskMetadata `gorm:"type:JSONB" json:"metadata"`

	Name  string `json:"name"`
	Notes string `json:"notes"`

	CreatedByType   TaskCreatedByType `json:"createdByType"`
	CreatedByUserID *uint             `json:"createdByUserID"` // Only set if CreatedBy is TaskCreatedByUser
}

// Task-specific metadata
type TaskMetadata struct {
	PricingLookupTask *PricingLookupTask `gorm:"type:JSONB" json:"pricingLookupTask,omitempty"`
	DraftEmailTask    *EmailTask         `gorm:"type:JSONB" json:"draftEmailTask,omitempty"`
	AddEmailLabelTask *AddEmailLabelTask `gorm:"type:JSONB" json:"addEmailLabelTask,omitempty"`

	// Future; additional metadata for each task based on Action type
	NotifyUserTask       *NotifyUserTask          `gorm:"type:JSONB" json:"notifyUserTask,omitempty"`
	SubmitQuoteTask      *SubmitQuoteToPortalTask `gorm:"type:JSONB" json:"submitQuoteTask,omitempty"`
	SubmitQuoteToTMSTask *SubmitQuoteToTMSTask    `gorm:"type:JSONB" json:"submitQuoteToTMSTask,omitempty"`
}

type PricingLookupTask struct {
	QuoteSelectionStrategy QuoteSelectionStrategy `json:"quoteSelectionStrategy,omitempty"`
	QuoteSourceOverride    models.QuoteSource     `json:"quoteSourceOverride,omitempty"`
}

type EmailTask struct {
	EmailTemplateID *uint `json:"emailTemplateId"`
	// No subject template needed for `draftReplyEmail` as thread already has subject
	// Future: LLM generates email based on user's recent emails/user prompt
}

type AddEmailLabelTask struct {
	Label models.EmailClientLabel `json:"label"`
}

// v1.5: Notify user of generated draft email
// For now, 1 standard notification per agent completion; can be customized later if needed
type NotifyUserTask struct {
	Channel string `json:"channel"` // "email", "slack", "teams", etc.

	// Future
	MessageTemplate    string `json:"messageTemplate"`
	FrequencyType      string `json:"frequency"`          // "time", "agent_invocation"
	FrequencyUnit      string `json:"frequencyUnit"`      // for time: "minutes", "hours", "days"; OR "invocation"
	FrequencyThreshold int    `json:"frequencyThreshold"` // for time: 1hr, 2hr, 3hr, etc. OR for invocation: 1, 2, etc
}

// Future: Submit quote to portal (e.g. E2Open, FreightView)
type SubmitQuoteToPortalTask struct {
	IntegrationID uint   `json:"integrationId"`
	URL           string `json:"url"` // Dynamic URL of that specific quote request to submit to
}

// Future: Submit quote to TMS
type SubmitQuoteToTMSTask struct {
	IntegrationID uint `json:"tmsId"`
}

// Implement Scan and Value for TaskMetadata
func (t *TaskMetadata) Scan(value any) error {
	if value == nil {
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for TaskMetadata: %T", value)
	}
	var taskMetadata TaskMetadata
	if err := json.Unmarshal(val, &taskMetadata); err != nil {
		return err
	}
	*t = taskMetadata
	return nil
}

func (t TaskMetadata) Value() (driver.Value, error) {
	if t == (TaskMetadata{}) {
		return nil, nil
	}

	return json.Marshal(t)
}

var (
	_ sql.Scanner   = &TaskMetadata{}
	_ driver.Valuer = &TaskMetadata{}
)

// --------------------------------------------------------------
// 						 	ENUMS
// --------------------------------------------------------------

type QuoteSelectionStrategy string

const (
	SourceOverrideStrategy QuoteSelectionStrategy = "sourceOverride"
	MostConfidentStrategy  QuoteSelectionStrategy = "mostConfident" //nolint:lll // only available for GS quotes; what to do if service has GS + DAT?
	LowestCostStrategy     QuoteSelectionStrategy = "lowestCost"
	HighestCostStrategy    QuoteSelectionStrategy = "highestCost"

	// Future: Ask LLM to decide which quote to use
	LLMDecisionStrategy QuoteSelectionStrategy = "llmDecision"
)

type QuickQuoteAgentAction string

const (
	PricingLookupAction   QuickQuoteAgentAction = "pricingLookup"
	DraftReplyEmailAction QuickQuoteAgentAction = "draftReplyEmail"
	SendReplyEmailAction  QuickQuoteAgentAction = "sendReplyEmail"
	AddEmailLabelAction   QuickQuoteAgentAction = "addEmailLabel"

	// Future
	DraftNewEmailAction        QuickQuoteAgentAction = "draftNewEmail"
	SendNewEmailAction         QuickQuoteAgentAction = "sendNewEmail"
	NotifyUserAction           QuickQuoteAgentAction = "notifyUser"
	SubmitQuoteToPortalAction  QuickQuoteAgentAction = "submitQuoteToPortal" // submit to bidding portal (e.g. E2Open)
	SubmitQuoteToTMSAction     QuickQuoteAgentAction = "submitQuoteToTMS"
	SubmitQuoteToServiceAction QuickQuoteAgentAction = "submitQuoteToService"
)

type AgentCreatedByType string

const (
	AgentCreatedByUser   AgentCreatedByType = "user"
	AgentCreatedBySystem AgentCreatedByType = "system"
)

type TaskCreatedByType string

const (
	TaskCreatedByUser   TaskCreatedByType = "user"
	TaskCreatedBySystem TaskCreatedByType = "system"
)
